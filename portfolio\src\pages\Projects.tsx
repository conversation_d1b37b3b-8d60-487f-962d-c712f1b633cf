import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { projects as allProjects } from '../shared/projects.data';
import WebBackground from '../components/WebBackground';

export default function Projects() {
  const [q, setQ] = useState('');
  const [tag, setTag] = useState('All');
  const tags = useMemo(() => ['All', ...Array.from(new Set(allProjects.flatMap(p => p.tech)))], []);
  const filtered = useMemo(() => allProjects.filter(p =>
    (tag === 'All' || p.tech.includes(tag)) && (p.name.toLowerCase().includes(q.toLowerCase()) || p.summary.toLowerCase().includes(q.toLowerCase()))
  ), [q, tag]);

  return (
    <>
      {/* Web background with parallax for projects */}
      <WebBackground
        variant="page"
        animationSpeed={0.6}
        density="medium"
        colorTheme="auto"
        enableCursorInteraction={true}
        enableParticles={true}
        className="opacity-25"
      />

      <section className="relative py-12">
      <Helmet><title>Projects — Ragul</title></Helmet>
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 justify-between">
        <h1 className="text-3xl font-bold">Projects</h1>
        <div className="flex gap-2 items-center">
          <input value={q} onChange={e => setQ(e.target.value)} placeholder="Search projects" className="px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent" />
          <select value={tag} onChange={e => setTag(e.target.value)} className="px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent">
            {tags.map(t => <option key={t} value={t}>{t}</option>)}
          </select>
        </div>
      </div>

      <ul className="mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-5">
        {filtered.map(p => (
          <li key={p.slug} className="card p-4">
            <img src={p.thumbnail} alt="" className="rounded-lg aspect-video object-cover" loading="lazy" />
            <h3 className="mt-3 font-semibold">{p.name}</h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">{p.summary}</p>
            <div className="mt-3 flex flex-wrap gap-1">
              {p.tech.map(t => <span key={t} className="text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-900">{t}</span>)}
            </div>
            <div className="mt-4 flex gap-2">
              <a href={p.demo} target="_blank" rel="noreferrer" className="btn btn-primary">Live</a>
              <a href={p.repo} target="_blank" rel="noreferrer" className="btn btn-ghost">Code</a>
            </div>
          </li>
        ))}
      </ul>
    </section>
    </>
  );
}

