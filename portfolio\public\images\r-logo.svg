<!-- R Logo SVG - Purple metallic design -->
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Metallic gradient for 3D effect -->
    <linearGradient id="metallicGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e879f9;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#c084fc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#9333ea;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b21a8;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="6" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle for glow effect -->
  <circle cx="100" cy="100" r="90" fill="none" stroke="url(#metallicGradient)" stroke-width="2" opacity="0.3" filter="url(#glow)"/>
  
  <!-- Main R letter -->
  <path d="M 50 50 L 50 150 L 70 150 L 70 110 L 110 110 C 125 110 135 100 135 85 L 135 75 C 135 60 125 50 110 50 Z M 70 70 L 110 70 C 115 70 115 75 115 75 L 115 85 C 115 90 110 90 110 90 L 70 90 Z M 90 110 L 130 150 L 155 150 L 110 110" 
        fill="url(#metallicGradient)" 
        filter="url(#shadow)"
        stroke="#9333ea" 
        stroke-width="1"/>
  
  <!-- Highlight for 3D effect -->
  <path d="M 55 55 L 55 145 M 75 75 L 105 75 M 75 85 L 105 85" 
        stroke="#e879f9" 
        stroke-width="2" 
        opacity="0.6"/>
</svg>
