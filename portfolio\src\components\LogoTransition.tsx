// LogoTransition.tsx
// Handles the smooth transition of the "R" logo from loading screen to navbar
// Creates a seamless cinematic effect when loading completes

import { motion, useAnimationControls } from "framer-motion";
import { useEffect, useState } from "react";

interface LogoTransitionProps {
  isLoading: boolean;
  onTransitionComplete?: () => void;
  targetPosition?: { x: number; y: number };
}

export default function LogoTransition({ 
  isLoading, 
  onTransitionComplete,
  targetPosition = { x: -200, y: -300 } // Default navbar position
}: LogoTransitionProps) {
  const [showTransition, setShowTransition] = useState(false);
  const controls = useAnimationControls();

  useEffect(() => {
    if (!isLoading && !showTransition) {
      setShowTransition(true);
      
      // Start the transition animation
      const animateToNavbar = async () => {
        // First shrink and move to navbar position
        await controls.start({
          scale: 0.3,
          x: targetPosition.x,
          y: targetPosition.y,
          transition: {
            duration: 1.2,
            ease: [0.22, 1, 0.36, 1]
          }
        });
        
        // Then fade out the transition logo
        await controls.start({
          opacity: 0,
          transition: {
            duration: 0.3
          }
        });
        
        onTransitionComplete?.();
      };
      
      animateToNavbar();
    }
  }, [isLoading, showTransition, controls, targetPosition, onTransitionComplete]);

  if (!showTransition) return null;

  return (
    <motion.div
      className="fixed inset-0 pointer-events-none"
      style={{ zIndex: 10000 }}
      initial={{ opacity: 1 }}
      animate={controls}
    >
      {/* Transition Logo */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <motion.div
          className="text-8xl md:text-9xl lg:text-[8rem] font-black text-purple-500"
          style={{
            fontFamily: "'Inter', sans-serif",
            textShadow: `
              0 0 20px rgba(147, 51, 234, 0.8),
              0 0 40px rgba(147, 51, 234, 0.6),
              0 0 60px rgba(147, 51, 234, 0.4)
            `,
            filter: 'drop-shadow(0 0 30px rgba(147, 51, 234, 0.7))'
          }}
        >
          R
        </motion.div>
      </div>
      
      {/* Fade overlay to smooth transition */}
      <motion.div
        className="absolute inset-0 bg-black"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.3, 0] }}
        transition={{ duration: 1.2 }}
      />
    </motion.div>
  );
}

// Hook for managing the complete loading to navbar transition
export function useLogoTransition() {
  const [isLoading, setIsLoading] = useState(true);
  const [showTransition, setShowTransition] = useState(false);
  const [transitionComplete, setTransitionComplete] = useState(false);

  const handleLoadingComplete = () => {
    setIsLoading(false);
    setShowTransition(true);
  };

  const handleTransitionComplete = () => {
    setShowTransition(false);
    setTransitionComplete(true);
  };

  return {
    isLoading,
    showTransition,
    transitionComplete,
    handleLoadingComplete,
    handleTransitionComplete
  };
}
