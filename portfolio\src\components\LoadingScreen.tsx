// OptimizedPortfolioLoader.tsx
// Production-ready React + TailwindCSS + Framer Motion loading screen with:
// - Smooth morphing logo (circle → "R" → rocket) with better visual flow
// - Optimized gradient background with improved color harmony
// - Enhanced particle system with performance controls
// - Prominent progress indicator with percentage
// - Skip button for user control
// - Improved accessibility and mobile responsiveness
// - Reduced duration (3000ms default) for better UX

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { motion, useAnimationControls, useMotionValue, useReducedMotion, useTransform } from "framer-motion";
import FullScreenWebBackground, { getResponsiveOpacity, getResponsiveAnimationSpeed } from "./FullScreenWebBackground";
import SpiderManSilhouette from "./SpiderManSilhouette";

const MESSAGES = [
  "Initializing creative workspace...",
  "Loading portfolio assets...",
  "Optimizing user experience...",
  "Preparing interactive elements...",
  "Almost ready to showcase...",
  "Welcome to my digital space!"
];

interface PortfolioLoaderProps {
  onFinish?: () => void;
  duration?: number;
  allowSkip?: boolean;
  enableSpiderMan?: boolean;
}

export default function PortfolioLoader({ onFinish, duration = 3000, allowSkip = true, enableSpiderMan = true }: PortfolioLoaderProps) {
  const prefersReducedMotion = useReducedMotion();
  const [done, setDone] = useState(false);
  const [skipped, setSkipped] = useState(false);
  const startRef = useRef<number | null>(null);
  const [progress, setProgress] = useState(0);
  const p = useMotionValue(0);
  const controls = useAnimationControls();

  // Drive progress 0 → 1 over duration; mark done at end
  useEffect(() => {
    if (skipped) return;
    let raf = 0;
    const tick = (t: number) => {
      if (startRef.current == null) startRef.current = t;
      const elapsed = t - startRef.current;
      const prog = Math.min(1, elapsed / duration);
      setProgress(prog);
      p.set(prog);
      if (prog < 1) raf = requestAnimationFrame(tick);
      else setDone(true);
    };
    raf = requestAnimationFrame(tick);
    return () => cancelAnimationFrame(raf);
  }, [duration, p, skipped]);

  // Handle skip functionality
  const handleSkip = useCallback(() => {
    setSkipped(true);
    setProgress(1);
    p.set(1);
    setDone(true);
  }, [p]);

  // When done: animate out container, then onFinish
  useEffect(() => {
    if (!done) return;
    controls.start({
      opacity: 0,
      scale: 0.95,
      y: -20,
      transition: { duration: 0.5, ease: [0.22, 1, 0.36, 1] }
    }).then(() => onFinish?.());
  }, [done, controls, onFinish]);

  // Typewriter: cycles messages every 0.6–0.8s with typing effect
  const { typed } = useTypewriter(MESSAGES, 600, 800);

  // Improved paths for smoother visual morphing (circle → R → rocket)
  const paths = useMemo(() => ({
    circle: "M50,50 m-25,0 a25,25 0 1,0 50,0 a25,25 0 1,0 -50,0", // Perfect circle
    rshape: "M25,25 L55,25 C65,25 70,30 70,40 C70,50 65,55 55,55 L45,55 M45,55 L65,80", // Cleaner 'R'
    rocket: "M50,20 L58,35 L50,55 L42,35 Z M50,20 L50,15 M42,55 L46,58 L44,65 M58,55 L54,58 L56,65" // Refined rocket
  }), []);

  // Smoother animation timing with better visual flow
  const circleLen = useTransform(p, [0, 0.25], [0, 1]);
  const rLen = useTransform(p, [0.25, 0.65], [0, 1]);
  const rocketLen = useTransform(p, [0.65, 1], [0, 1]);

  const circleOpacity = useTransform(p, [0, 0.25, 0.35], [1, 1, 0]);
  const rOpacity = useTransform(p, [0.25, 0.5, 0.75], [0, 1, 0]);
  const rocketOpacity = useTransform(p, [0.65, 0.85, 1], [0, 1, 1]);

  // Improved color harmony: blue → purple → cyan (more professional)
  const hue = useTransform(p, [0, 1], [220, 280]); // blue → purple
  const bgStyle = ({ ["--h" as any]: hue } as unknown) as import("framer-motion").MotionStyle;

  // Progress percentage for accessibility
  const progressPercent = Math.round(progress * 100);

  if (prefersReducedMotion) {
    // Enhanced reduced motion: Better contrast and larger progress indicator
    return (
      <div role="status" aria-live="polite" className="fixed inset-0 z-[60] grid place-items-center bg-gray-900 text-white">
        <div className="text-center max-w-sm mx-auto px-6">
          <svg width="120" height="120" viewBox="0 0 100 100" className="mx-auto mb-6">
            <path d={paths.rshape} fill="none" stroke="hsl(240 100% 70%)" strokeWidth="4" />
          </svg>
          <p className="text-base font-medium text-white mb-2">{typed}</p>
          <div className="text-sm text-gray-300 mb-4" aria-live="polite">
            Loading {progressPercent}%
          </div>
          <div className="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-500 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
          {allowSkip && progressPercent < 90 && (
            <button
              onClick={handleSkip}
              className="mt-6 px-4 py-2 text-sm text-gray-300 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
            >
              Skip intro
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      role="status"
      aria-live="polite"
      aria-label={`Loading ${progressPercent}%`}
      className="fixed inset-0 z-[60] overflow-hidden"
      animate={controls}
      initial={{ opacity: 1, scale: 1 }}
      style={bgStyle}
    >
      {/* FIXED: Full-screen Spider-Man web background with expanding animation */}
      <FullScreenWebBackground
        variant="loading"
        animationSpeed={getResponsiveAnimationSpeed(1.5)}
        density="medium"
        colorTheme="blue"
        enableCursorInteraction={true}
        enableParticles={true}
        opacity={getResponsiveOpacity('loading')}
      />

      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 -z-10 animate-[gradShift_8s_ease-in-out_infinite] [background:radial-gradient(80vmax_50vmax_at_50%_50%,hsl(var(--h)_60%_8%)/.3,transparent_60%)]" />

      {/* Spider-Man swing animation */}
      <SpiderManSilhouette
        enabled={enableSpiderMan}
        delay={1500}
        className="z-0"
      />

      {/* Skip button */}
      {allowSkip && !done && progressPercent < 90 && (
        <motion.button
          onClick={handleSkip}
          className="absolute top-6 right-6 px-4 py-2 text-sm text-white/70 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 z-10"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Skip intro
        </motion.button>
      )}

      {/* Enhanced centerpiece with responsive sizing */}
      <div className="relative grid place-items-center h-full px-4">
        <motion.div
          className="relative text-center"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
        >
          <motion.svg
            width="200"
            height="200"
            viewBox="0 0 100 100"
            className="mx-auto drop-shadow-[0_8px_32px_rgba(59,130,246,0.4)] sm:w-[240px] sm:h-[240px]"
          >
            <defs>
              <linearGradient id="strokeGrad" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="hsl(var(--h) 85% 75%)" />
                <stop offset="100%" stopColor="hsl(calc(var(--h)+20) 85% 65%)" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
              <linearGradient id="shine" x1="0" x2="1">
                <stop offset="0%" stopColor="white" stopOpacity="0" />
                <stop offset="50%" stopColor="white" stopOpacity="0.3" />
                <stop offset="100%" stopColor="white" stopOpacity="0" />
              </linearGradient>
            </defs>

            {/* Circle */}
            <motion.path
              d={paths.circle}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="3"
              style={{ pathLength: circleLen, opacity: circleOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* R initial */}
            <motion.path
              d={paths.rshape}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="4"
              style={{ pathLength: rLen, opacity: rOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Rocket */}
            <motion.path
              d={paths.rocket}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="3"
              style={{ pathLength: rocketLen, opacity: rocketOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Enhanced shine effect */}
            <motion.rect
              x="-40"
              y="-10"
              width="180"
              height="120"
              fill="url(#shine)"
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 120, opacity: [0, 0.4, 0] }}
              transition={{ duration: 2.2, repeat: Infinity, repeatDelay: 1.5, ease: "easeInOut" }}
              style={{ mixBlendMode: "screen" }}
            />
          </motion.svg>

          {/* Enhanced typewriter messages with better contrast */}
          <motion.p
            className="mt-8 text-base font-medium text-white text-center px-4 drop-shadow-lg"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            aria-live="polite"
          >
            {typed}
          </motion.p>

          {/* Progress percentage display */}
          <motion.div
            className="mt-4 text-sm text-white/80 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            aria-live="polite"
          >
            {progressPercent}%
          </motion.div>
        </motion.div>
      </div>

      {/* Enhanced progress bar with better visibility */}
      <div className="absolute bottom-0 left-0 right-0 h-2 bg-black/30 backdrop-blur-sm">
        <motion.div
          className="relative h-full overflow-hidden"
          style={{
            width: `${progressPercent}%`,
            background: `linear-gradient(90deg, hsl(${220 + (60 * progress)},85%,65%), hsl(${240 + (60 * progress)},85%,60%))`,
            boxShadow: "0 0 20px 3px hsl(var(--h) 85% 60% / 0.5)"
          }}
          initial={{ width: 0 }}
          animate={{ width: `${progressPercent}%` }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {/* Enhanced shimmer effect */}
          <div className="absolute inset-0 bg-[linear-gradient(110deg,transparent,rgba(255,255,255,.3),transparent)] animate-[shimmer_1.2s_ease-in-out_infinite]" />
        </motion.div>
      </div>

      {/* Optimized keyframes */}
      <style>{`
        @keyframes gradShift {
          0%,100% { filter: hue-rotate(0deg) saturate(1); }
          50% { filter: hue-rotate(20deg) saturate(1.05); }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </motion.div>
  );
}

/* Enhanced Typewriter Hook: Optimized timing and smoother transitions */
function useTypewriter(messages: string[], min = 600, max = 800) {
  const [msgIndex, setMsgIndex] = useState(0);
  const [typed, setTyped] = useState("");
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const scheduleNext = useCallback(() => {
    const next = (msgIndex + 1) % messages.length;
    const delay = Math.floor(min + Math.random() * (max - min));
    timerRef.current = setTimeout(() => {
      setMsgIndex(next);
      setTyped(""); // restart typing for next message
    }, delay);
  }, [msgIndex, messages.length, min, max]);

  useEffect(() => {
    // Type the current message with variable speed for more natural feel
    const str = messages[msgIndex];
    let i = 0;
    let cancelled = false;
    const type = () => {
      if (cancelled) return;
      setTyped(str.slice(0, i));
      i++;
      if (i <= str.length) {
        // Variable typing speed: faster for spaces, slower for punctuation
        const char = str[i - 1];
        const speed = char === ' ' ? 15 : char === '.' || char === '!' ? 150 : 35;
        setTimeout(type, speed);
      } else {
        scheduleNext();
      }
    };
    type();
    return () => {
      cancelled = true;
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [msgIndex, messages, scheduleNext]);

  return { typed };
}

