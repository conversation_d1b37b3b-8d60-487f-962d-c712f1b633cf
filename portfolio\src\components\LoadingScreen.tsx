// PortfolioLoader.tsx
// React + Vite + TailwindCSS + Framer Motion loading screen with:
// - Morphing centerpiece (fingerprint → "R" initial → rocket) via path drawing + crossfade
// - Animated gradient background + glowing particle parallax
// - Typewriter cycling messages
// - Progress bar with shimmer + hue shift
// - Reduced-motion fallback
// - Fades out after duration (default 5000ms), triggers onFinish

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { motion, useAnimationControls, useMotionValue, useReducedMotion, useTransform } from "framer-motion";
import ParticleBackground from "./ParticleBackground";

const MESSAGES = [
  "Booting creativity engines...",
  "Rendering your awesomeness...",
  "Securing coffee supply...",
  "Deploying ideas into the wild...",
  "Tuning micro-interactions...",
  "Optimizing pixels & performance..."
];

interface PortfolioLoaderProps {
  onFinish?: () => void;
  duration?: number;
}

export default function PortfolioLoader({ onFinish, duration = 5000 }: PortfolioLoaderProps) {
  const prefersReducedMotion = useReducedMotion();
  const [done, setDone] = useState(false);
  const startRef = useRef<number | null>(null);
  const [progress, setProgress] = useState(0);
  const p = useMotionValue(0);
  const controls = useAnimationControls();

  // Drive progress 0 → 1 over duration; mark done at end
  useEffect(() => {
    let raf = 0;
    const tick = (t: number) => {
      if (startRef.current == null) startRef.current = t;
      const elapsed = t - startRef.current;
      const prog = Math.min(1, elapsed / duration);
      setProgress(prog);
      p.set(prog);
      if (prog < 1) raf = requestAnimationFrame(tick);
      else setDone(true);
    };
    raf = requestAnimationFrame(tick);
    return () => cancelAnimationFrame(raf);
  }, [duration, p]);

  // When done: animate out container, then onFinish
  useEffect(() => {
    if (!done) return;
    controls.start({ opacity: 0, scale: 0.98, transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] } })
      .then(() => onFinish?.());
  }, [done, controls, onFinish]);

  // Typewriter: cycles messages every 0.7–0.9s with typing effect
  const { currentMessage, typed } = useTypewriter(MESSAGES, 700, 900);

  // Paths for illusion of morph (we crossfade + draw with pathLength)
  const paths = useMemo(() => ({
    fingerprint: "M50,10 C75,10 90,30 90,55 C90,80 70,95 50,95 C30,95 10,80 10,55 C10,30 25,10 50,10 Z",
    rshape: "M22,22 L55,22 C70,22 80,32 80,45 C80,58 70,68 55,68 L40,68 M40,68 L70,95", // 'R'
    rocket: "M50,18 L62,38 L50,58 L38,38 Z M50,18 L50,8 M38,58 L44,60 L42,68 M62,58 L56,60 L58,68" // simple rocket + fins
  }), []);

  // Map global progress to each path's draw and opacity (staggered morph illusion)
  const fpLen = useTransform(p, [0, 0.33], [0, 1]);
  const rLen = useTransform(p, [0.2, 0.66], [0, 1]);
  const rkLen = useTransform(p, [0.5, 1], [0, 1]);

  const fpOpacity = useTransform(p, [0, 0.33, 0.45], [1, 1, 0]);
  const rOpacity = useTransform(p, [0.2, 0.5, 0.75], [0, 1, 0]);
  const rkOpacity = useTransform(p, [0.55, 0.85, 1], [0, 1, 1]);

  // Hue shift tied to progress for gradient + progress bar sync
  const hue = useTransform(p, [0, 1], [250, 330]); // purple → magenta
  // Animated gradient via CSS variables; allow MotionValue via type assertion for CSS var key
  const bgStyle = ({ ["--h" as any]: hue } as unknown) as import("framer-motion").MotionStyle;

  if (prefersReducedMotion) {
    // Reduced motion: Static logo + simple progress bar only
    return (
      <div role="status" aria-live="polite" className="fixed inset-0 z-[60] grid place-items-center bg-black text-white">
        <div className="text-center">
          <svg width="160" height="120" viewBox="0 0 100 110" className="mx-auto">
            <path d={paths.rshape} fill="none" stroke="hsl(260 80% 60%)" strokeWidth="6" />
          </svg>
          <p className="mt-3 text-sm text-neutral-300">{typed}</p>
          <div className="mt-5 h-1 w-64 bg-neutral-800 rounded overflow-hidden mx-auto">
            <div className="h-full bg-[hsl(var(--h)_90%_60%)]" style={{ width: `${progress * 100}%` }} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      role="status"
      aria-live="polite"
      className="fixed inset-0 z-[60] overflow-hidden"
      animate={controls}
      initial={{ opacity: 1, scale: 1 }}
      style={bgStyle}
    >
      {/* Animated multi-stop gradient background */}
      <div className="absolute inset-0 -z-20 animate-[gradShift_12s_ease-in-out_infinite] [background:radial-gradient(120vmax_80vmax_at_10%_10%,hsl(var(--h)_90%_20%)/.6,transparent_40%),radial-gradient(120vmax_80vmax_at_90%_90%,hsl(calc(var(--h)+30)_90%_15%)/.6,transparent_35%),linear-gradient(135deg,hsl(calc(var(--h)-30)_80%_10%),hsl(calc(var(--h)+30)_80%_8%))]" />

      {/* Glowing parallax particles */}
      <ParticleBackground progress={progress} fadeOut={done} />

      {/* Centerpiece morphing SVG with glassy shine */}
      <div className="relative grid place-items-center h-full">
        <motion.div
          className="relative"
          initial={{ scale: 0.96, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
        >
          <motion.svg width="260" height="260" viewBox="0 0 100 110" className="drop-shadow-[0_10px_40px_rgba(108,92,231,0.45)]">
            <defs>
              <linearGradient id="strokeGrad" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="hsl(var(--h) 90% 70%)" />
                <stop offset="100%" stopColor="hsl(calc(var(--h)+30) 90% 60%)" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="1.2" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
              {/* Shine gradient for glassy sweep */}
              <linearGradient id="shine" x1="0" x2="1">
                <stop offset="0%" stopColor="white" stopOpacity="0" />
                <stop offset="50%" stopColor="white" stopOpacity="0.45" />
                <stop offset="100%" stopColor="white" stopOpacity="0" />
              </linearGradient>
            </defs>

            {/* Fingerprint */}
            <motion.path
              d={paths.fingerprint}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="3.5"
              style={{ pathLength: fpLen, opacity: fpOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* R initial */}
            <motion.path
              d={paths.rshape}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="6"
              style={{ pathLength: rLen, opacity: rOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Rocket */}
            <motion.path
              d={paths.rocket}
              fill="none"
              stroke="url(#strokeGrad)"
              strokeWidth="3.5"
              style={{ pathLength: rkLen, opacity: rkOpacity, filter: "url(#glow)" }}
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Moving glassy shine overlay */}
            <motion.rect
              x="-50"
              y="-10"
              width="200"
              height="160"
              fill="url(#shine)"
              initial={{ x: -120, opacity: 0.0 }}
              animate={{ x: 140, opacity: [0, 0.5, 0] }}
              transition={{ duration: 2.6, repeat: Infinity, repeatDelay: 1.2, ease: "easeInOut" }}
              style={{ mixBlendMode: "screen" }}
            />
          </motion.svg>

          {/* Witty typewriter messages */}
          <motion.p
            className="mt-6 text-sm text-neutral-200 text-center"
            initial={{ opacity: 0, y: 6 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.35 }}
            aria-live="polite"
          >
            {typed}
          </motion.p>
        </motion.div>
      </div>

      {/* Bottom progress bar with shimmer + hue sync */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20">
        <div
          className="relative h-full overflow-hidden"
          style={{
            width: `${Math.min(100, Math.max(0, progress * 100))}%`,
            background: `linear-gradient(90deg, hsl(${250 + (80 * progress)},90%,60%), hsl(${280 + (80 * progress)},90%,55%))`,
            boxShadow: "0 0 16px 2px hsl(var(--h) 90% 55% / 0.6)"
          }}
        >
          {/* Shimmer */}
          <div className="absolute inset-0 bg-[linear-gradient(120deg,transparent,rgba(255,255,255,.4),transparent)] animate-[shimmer_1.4s_linear_infinite]" />
        </div>
      </div>

      {/* Local keyframes for gradient + shimmer */}
      <style>{`
        @keyframes gradShift {
          0%,100% { filter: hue-rotate(0deg) saturate(1); }
          50% { filter: hue-rotate(30deg) saturate(1.1); }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </motion.div>
  );
}

/* Hook: Typewriter cycling through messages with 700–900ms cadence */
function useTypewriter(messages: string[], min = 700, max = 900) {
  const [msgIndex, setMsgIndex] = useState(0);
  const [typed, setTyped] = useState("");
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const scheduleNext = useCallback(() => {
    const next = (msgIndex + 1) % messages.length;
    const delay = Math.floor(min + Math.random() * (max - min));
    timerRef.current = setTimeout(() => {
      setMsgIndex(next);
      setTyped(""); // restart typing for next message
    }, delay);
  }, [msgIndex, messages.length, min, max]);

  useEffect(() => {
    // Type the current message
    const str = messages[msgIndex];
    let i = 0;
    let cancelled = false;
    const type = () => {
      if (cancelled) return;
      setTyped(str.slice(0, i));
      i++;
      if (i <= str.length) {
        setTimeout(type, 28); // typing speed
      } else {
        scheduleNext();
      }
    };
    type();
    return () => {
      cancelled = true;
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [msgIndex, messages, scheduleNext]);

  return { currentMessage: messages[msgIndex], typed };
}

