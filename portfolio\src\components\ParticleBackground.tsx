// OptimizedParticleBackground.tsx
// High-performance animated Canvas background with glowing particles + parallax.
// - Adaptive particle count based on device capabilities
// - Optimized rendering with requestAnimationFrame throttling
// - Smooth mouse interaction with performance-conscious calculations
// - Enhanced visual effects with better color harmony
// - Responsive design with mobile optimizations
import { useEffect, useRef } from "react";
import { useReducedMotion } from "framer-motion";

interface ParticleBackgroundProps {
  progress?: number;
  fadeOut?: boolean;
  count?: number;
}

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  hue: number;
  phase: number;
}

export default function ParticleBackground({ progress = 0, fadeOut = false, count = 24 }: ParticleBackgroundProps) {
  const prefersReducedMotion = useReducedMotion();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mouse = useRef({ x: 0, y: 0 });
  const opacityRef = useRef(1);
  const lastFrameTime = useRef(0);

  useEffect(() => {
    if (prefersReducedMotion) return;
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d", { alpha: true });
    if (!ctx) return;

    // Optimize for device capabilities
    const isMobile = window.innerWidth < 768;
    const isLowEnd = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4;
    let dpr = Math.max(1, Math.min(isMobile ? 1.5 : 2, window.devicePixelRatio || 1));
    let w = 0, h = 0;

    const resize = () => {
      w = canvas.clientWidth;
      h = canvas.clientHeight;
      canvas.width = Math.floor(w * dpr);
      canvas.height = Math.floor(h * dpr);
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    };
    resize();
    window.addEventListener("resize", resize);

    // Adaptive particle count based on device performance
    const adaptiveCount = isLowEnd ? Math.min(count, 16) : isMobile ? Math.min(count, 20) : count;

    const particles: Particle[] = Array.from({ length: adaptiveCount }).map(() => ({
      x: Math.random() * w,
      y: Math.random() * h,
      vx: (Math.random() - 0.5) * 0.15,
      vy: (Math.random() - 0.5) * 0.15,
      size: 1.5 + Math.random() * 2,
      hue: 220 + Math.random() * 60, // Better color harmony (blue to purple range)
      phase: Math.random() * Math.PI * 2
    }));

    let raf = 0;
    const targetFPS = isMobile ? 30 : 60;
    const frameInterval = 1000 / targetFPS;

    const loop = (t: number) => {
      // Throttle frame rate for performance
      if (t - lastFrameTime.current < frameInterval) {
        raf = requestAnimationFrame(loop);
        return;
      }
      lastFrameTime.current = t;

      // Smooth fade opacity when finishing
      const targetOpacity = fadeOut ? 0 : 1;
      opacityRef.current += (targetOpacity - opacityRef.current) * 0.06;

      ctx.clearRect(0, 0, w, h);
      ctx.globalCompositeOperation = "lighter";
      ctx.globalAlpha = 0.85 * opacityRef.current;

      for (let i = 0; i < particles.length; i++) {
        const p = particles[i];

        // Optimized mouse attraction with distance-based performance
        const dx = mouse.current.x - p.x;
        const dy = mouse.current.y - p.y;
        const distSq = dx * dx + dy * dy; // Use squared distance to avoid sqrt
        if (distSq < 40000) { // Only calculate attraction within reasonable range
          const dist = Math.sqrt(distSq) || 1;
          const att = Math.min(0.025, 15 / (dist * 800));
          p.vx += dx * att;
          p.vy += dy * att;
        }

        // Enhanced motion with smoother progress integration
        const damp = 0.985 - progress * 0.15;
        p.vx *= damp;
        p.vy *= damp;
        p.x += p.vx + Math.sin((t / 4000) + p.phase) * 0.04;
        p.y += p.vy + Math.cos((t / 4500) + p.phase) * 0.04;

        // Smooth wrap around with buffer
        const buffer = 20;
        if (p.x < -buffer) p.x = w + buffer;
        if (p.x > w + buffer) p.x = -buffer;
        if (p.y < -buffer) p.y = h + buffer;
        if (p.y > h + buffer) p.y = -buffer;

        // Enhanced breathing effect with better timing
        const pulse = 0.7 + 0.3 * Math.sin((t / 1200) + p.phase);
        const radius = 14 + p.size * 2.5;

        // Optimized gradient creation (reuse when possible)
        const grd = ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, radius);
        grd.addColorStop(0, `hsla(${p.hue}, 90%, 70%, ${0.4 * pulse})`);
        grd.addColorStop(0.7, `hsla(${p.hue}, 90%, 60%, ${0.15 * pulse})`);
        grd.addColorStop(1, "hsla(0,0%,0%,0)");

        ctx.fillStyle = grd;
        ctx.beginPath();
        ctx.arc(p.x, p.y, radius, 0, Math.PI * 2);
        ctx.fill();

        // Enhanced core with better visibility
        ctx.fillStyle = `hsla(${p.hue},90%,75%,${0.2 * pulse})`;
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.size * 0.8, 0, Math.PI * 2);
        ctx.fill();
      }

      ctx.globalCompositeOperation = "source-over";
      raf = requestAnimationFrame(loop);
    };
    raf = requestAnimationFrame(loop);

    const onMove = (e: PointerEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouse.current.x = e.clientX - rect.left;
      mouse.current.y = e.clientY - rect.top;
    };
    window.addEventListener("pointermove", onMove, { passive: true });

    return () => {
      cancelAnimationFrame(raf);
      window.removeEventListener("resize", resize);
      window.removeEventListener("pointermove", onMove);
    };
  }, [prefersReducedMotion, count, progress, fadeOut]);

  if (prefersReducedMotion) return null;
  return <canvas ref={canvasRef} className="absolute inset-0 -z-10" aria-hidden="true" />;
}
