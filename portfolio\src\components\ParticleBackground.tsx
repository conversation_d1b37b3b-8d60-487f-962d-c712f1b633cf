// ParticleBackground.tsx
// Reusable animated Canvas background with glowing particles + parallax.
// - Limited particle count (default ~28) for performance
// - Reacts to mouse (gentle drift towards cursor)
// - Particles "breathe" (pulse brightness)
// - Fades out when fadeOut=true
import { useEffect, useRef } from "react";
import { useReducedMotion } from "framer-motion";

interface ParticleBackgroundProps {
  progress?: number;
  fadeOut?: boolean;
  count?: number;
}

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  hue: number;
  phase: number;
}

export default function ParticleBackground({ progress = 0, fadeOut = false, count = 28 }: ParticleBackgroundProps) {
  const prefersReducedMotion = useReducedMotion();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mouse = useRef({ x: 0, y: 0 });
  const opacityRef = useRef(1);

  useEffect(() => {
    if (prefersReducedMotion) return;
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d", { alpha: true });
    if (!ctx) return;

    let dpr = Math.max(1, Math.min(2, window.devicePixelRatio || 1));
    let w = 0, h = 0;

    const resize = () => {
      w = canvas.clientWidth;
      h = canvas.clientHeight;
      canvas.width = Math.floor(w * dpr);
      canvas.height = Math.floor(h * dpr);
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    };
    resize();
    window.addEventListener("resize", resize);

    const particles: Particle[] = Array.from({ length: count }).map(() => ({
      x: Math.random() * w,
      y: Math.random() * h,
      vx: (Math.random() - 0.5) * 0.2,
      vy: (Math.random() - 0.5) * 0.2,
      size: 1.2 + Math.random() * 2.2,
      hue: 250 + Math.random() * 80,
      phase: Math.random() * Math.PI * 2
    }));

    let raf = 0;
    const loop = (t: number) => {
      // fade opacity when finishing
      const targetOpacity = fadeOut ? 0 : 1;
      opacityRef.current += (targetOpacity - opacityRef.current) * 0.04;

      ctx.clearRect(0, 0, w, h);
      ctx.globalCompositeOperation = "lighter";
      ctx.globalAlpha = 0.82 * opacityRef.current;

      for (let i = 0; i < particles.length; i++) {
        const p = particles[i];

        // gentle attraction to cursor
        const dx = mouse.current.x - p.x;
        const dy = mouse.current.y - p.y;
        const dist = Math.hypot(dx, dy) || 1;
        const att = Math.min(0.03, 20 / (dist * 1000)); // subtle
        p.vx += dx * att;
        p.vy += dy * att;

        // base motion + parallax with progress (slower as we near finish)
        const damp = 0.98 - progress * 0.2;
        p.vx *= damp;
        p.vy *= damp;
        p.x += p.vx + Math.sin((t / 3000) + p.phase) * 0.05;
        p.y += p.vy + Math.cos((t / 3500) + p.phase) * 0.05;

        // wrap around
        if (p.x < -10) p.x = w + 10;
        if (p.x > w + 10) p.x = -10;
        if (p.y < -10) p.y = h + 10;
        if (p.y > h + 10) p.y = -10;

        // breathing brightness pulse
        const pulse = 0.6 + 0.4 * Math.sin((t / 1000) + p.phase);
        const grd = ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, 16 + p.size * 3);
        grd.addColorStop(0, `hsla(${p.hue}, 95%, 65%, ${0.35 * pulse})`);
        grd.addColorStop(1, "hsla(0,0%,0%,0)");

        ctx.fillStyle = grd;
        ctx.beginPath();
        ctx.arc(p.x, p.y, 16 + p.size * 3, 0, Math.PI * 2);
        ctx.fill();

        // core glow
        ctx.fillStyle = `hsla(${p.hue},95%,70%,${0.15 * pulse})`;
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
        ctx.fill();
      }

      ctx.globalCompositeOperation = "source-over";
      raf = requestAnimationFrame(loop);
    };
    raf = requestAnimationFrame(loop);

    const onMove = (e: PointerEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouse.current.x = e.clientX - rect.left;
      mouse.current.y = e.clientY - rect.top;
    };
    window.addEventListener("pointermove", onMove, { passive: true });

    return () => {
      cancelAnimationFrame(raf);
      window.removeEventListener("resize", resize);
      window.removeEventListener("pointermove", onMove);
    };
  }, [prefersReducedMotion, count, progress, fadeOut]);

  if (prefersReducedMotion) return null;
  return <canvas ref={canvasRef} className="absolute inset-0 -z-10" aria-hidden="true" />;
}
