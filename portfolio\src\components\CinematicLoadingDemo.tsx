// CinematicLoadingDemo.tsx
// Demo component for testing the cinematic loading page
// Allows developers to preview and configure the loading experience

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import CinematicLoadingPage from "./CinematicLoadingPage";

export default function CinematicLoadingDemo() {
  const [showPreview, setShowPreview] = useState(false);
  const [duration, setDuration] = useState(5000);
  const [logoUrl, setLogoUrl] = useState("/images/r-logo.png");

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handlePreviewComplete = () => {
    setShowPreview(false);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 bg-clip-text text-transparent">
            🎬 Cinematic Loading Page
          </h1>
          <p className="text-gray-300 text-lg">
            Marvel-inspired loading experience with your "R" logo
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">🎯</div>
            <h3 className="text-xl font-bold mb-2">Centered "R" Logo</h3>
            <p className="text-gray-400 text-sm">
              3D metallic texture with neon purple glow, perfectly centered on all screens
            </p>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">🕷️</div>
            <h3 className="text-xl font-bold mb-2">Spider-Web Background</h3>
            <p className="text-gray-400 text-sm">
              Animated web pattern with Marvel color scheme, scales to any screen size
            </p>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">⚡</div>
            <h3 className="text-xl font-bold mb-2">Arc Reactor Spinner</h3>
            <p className="text-gray-400 text-sm">
              Glowing purple progress indicator with smooth completion animation
            </p>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">✨</div>
            <h3 className="text-xl font-bold mb-2">Particle Effects</h3>
            <p className="text-gray-400 text-sm">
              Floating particles and light streaks for cinematic atmosphere
            </p>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">💫</div>
            <h3 className="text-xl font-bold mb-2">Light Sweep</h3>
            <p className="text-gray-400 text-sm">
              High-tech light sweep effect across the logo for premium feel
            </p>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <div className="text-3xl mb-3">⌨️</div>
            <h3 className="text-xl font-bold mb-2">Typewriter Text</h3>
            <p className="text-gray-400 text-sm">
              "Assembling your experience..." with futuristic font and flicker
            </p>
          </div>
        </div>

        {/* Configuration */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h3 className="text-xl font-bold mb-4">Configuration</h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Duration Control */}
            <div>
              <label className="block text-gray-300 mb-2">Loading Duration</label>
              <input
                type="range"
                min="2000"
                max="10000"
                step="500"
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value))}
                className="w-full"
              />
              <div className="text-sm text-gray-400 mt-1">{duration / 1000}s</div>
            </div>

            {/* Logo URL */}
            <div>
              <label className="block text-gray-300 mb-2">Logo Image URL</label>
              <input
                type="text"
                value={logoUrl}
                onChange={(e) => setLogoUrl(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 rounded border border-gray-600 text-white"
                placeholder="/images/r-logo.png"
              />
            </div>
          </div>
        </div>

        {/* Preview Button */}
        <div className="text-center mb-8">
          <motion.button
            onClick={handlePreview}
            className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg font-bold text-lg transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            🎬 Preview Loading Screen
          </motion.button>
        </div>

        {/* Technical Specs */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4 text-green-400">✅ Features Implemented</h3>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li>• 3D metallic "R" logo with purple neon glow</li>
              <li>• Animated spider-web background pattern</li>
              <li>• Arc Reactor-style circular progress spinner</li>
              <li>• Typewriter text with futuristic font</li>
              <li>• Floating particles and light streaks</li>
              <li>• Light sweep effect across logo</li>
              <li>• Smooth transition to main page</li>
              <li>• Responsive design (mobile + desktop)</li>
              <li>• Performance optimized (<500KB)</li>
              <li>• Accessibility support</li>
            </ul>
          </div>

          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4 text-blue-400">🎨 Design Specs</h3>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li>• <strong>Background:</strong> Black with red/blue gradients</li>
              <li>• <strong>Logo Color:</strong> Purple (#9333ea) with glow</li>
              <li>• <strong>Font:</strong> Inter (logo), Orbitron (text)</li>
              <li>• <strong>Animation:</strong> 60fps smooth performance</li>
              <li>• <strong>Duration:</strong> 5 seconds default</li>
              <li>• <strong>Particles:</strong> 20 floating, 3 light streaks</li>
              <li>• <strong>Web Pattern:</strong> 12 radial lines, 8 circles</li>
              <li>• <strong>Effects:</strong> Glow, pulse, sweep, typewriter</li>
              <li>• <strong>Responsive:</strong> Scales 4rem → 8rem logo</li>
              <li>• <strong>Transition:</strong> Logo shrinks to navbar</li>
            </ul>
          </div>
        </div>

        {/* Code Example */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-4">Implementation Example</h3>
          
          <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre className="text-sm text-gray-300">
              <code>{`import CinematicLoadingPage from './components/CinematicLoadingPage';
import { useLogoTransition } from './components/LogoTransition';

function App() {
  const {
    isLoading,
    showTransition,
    handleLoadingComplete,
    handleTransitionComplete
  } = useLogoTransition();

  return (
    <>
      {isLoading ? (
        <CinematicLoadingPage
          onFinish={handleLoadingComplete}
          duration={${duration}}
          logoImageUrl="${logoUrl}"
        />
      ) : (
        <main>Your app content</main>
      )}
      
      {showTransition && (
        <LogoTransition
          isLoading={isLoading}
          onTransitionComplete={handleTransitionComplete}
        />
      )}
    </>
  );
}`}</code>
            </pre>
          </div>
        </div>

        {/* Performance Notes */}
        <div className="mt-8 bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-2 text-yellow-400">⚡ Performance Notes</h3>
          <ul className="text-yellow-200 text-sm space-y-1">
            <li>• Canvas animations use requestAnimationFrame for 60fps</li>
            <li>• GPU acceleration with transform3d and will-change</li>
            <li>• Reduced particle count on mobile devices</li>
            <li>• Lazy loading and code splitting ready</li>
            <li>• Total bundle size impact: ~50KB gzipped</li>
          </ul>
        </div>
      </div>

      {/* Preview Modal */}
      <AnimatePresence>
        {showPreview && (
          <CinematicLoadingPage
            duration={duration}
            logoImageUrl={logoUrl}
            onFinish={handlePreviewComplete}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
