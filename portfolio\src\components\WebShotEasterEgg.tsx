// WebShotEasterEgg.tsx
// Easter egg: Double-click anywhere to trigger web shot animation
// Features: Radial web expansion, particle burst, sound effect (optional)

import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface WebShotProps {
  x: number;
  y: number;
  id: string;
}

export default function WebShotEasterEgg() {
  const [webShots, setWebShots] = useState<WebShotProps[]>([]);
  const lastShotTime = useRef(0);

  useEffect(() => {
    const handleDoubleClick = (e: MouseEvent) => {
      const now = Date.now();
      
      // Throttle to prevent spam
      if (now - lastShotTime.current < 500) return;
      lastShotTime.current = now;

      const newShot: WebShotProps = {
        x: e.clientX,
        y: e.clientY,
        id: `shot-${now}-${Math.random()}`
      };

      setWebShots(prev => [...prev, newShot]);

      // Remove after animation completes
      setTimeout(() => {
        setWebShots(prev => prev.filter(shot => shot.id !== newShot.id));
      }, 2000);
    };

    document.addEventListener('dblclick', handleDoubleClick);
    return () => document.removeEventListener('dblclick', handleDoubleClick);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      <AnimatePresence>
        {webShots.map(shot => (
          <WebShotAnimation key={shot.id} x={shot.x} y={shot.y} />
        ))}
      </AnimatePresence>
    </div>
  );
}

function WebShotAnimation({ x, y }: { x: number; y: number }) {
  return (
    <motion.div
      className="absolute"
      style={{ left: x, top: y }}
      initial={{ scale: 0, opacity: 1 }}
      animate={{ scale: 1, opacity: 0 }}
      exit={{ scale: 1.2, opacity: 0 }}
      transition={{ duration: 1.5, ease: "easeOut" }}
    >
      {/* Main web burst */}
      <svg
        width="200"
        height="200"
        viewBox="0 0 200 200"
        className="absolute -translate-x-1/2 -translate-y-1/2"
      >
        <defs>
          <radialGradient id="webGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="rgba(59, 130, 246, 0.8)" />
            <stop offset="70%" stopColor="rgba(59, 130, 246, 0.3)" />
            <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
          </radialGradient>
        </defs>

        {/* Radial web lines */}
        {Array.from({ length: 8 }).map((_, i) => {
          const angle = (i * 45) * (Math.PI / 180);
          const x1 = 100;
          const y1 = 100;
          const x2 = 100 + Math.cos(angle) * 90;
          const y2 = 100 + Math.sin(angle) * 90;

          return (
            <motion.line
              key={i}
              x1={x1}
              y1={y1}
              x2={x2}
              y2={y2}
              stroke="url(#webGradient)"
              strokeWidth="2"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: [0, 1, 0] }}
              transition={{
                duration: 1.2,
                delay: i * 0.05,
                ease: "easeOut"
              }}
            />
          );
        })}

        {/* Concentric circles */}
        {[30, 50, 70, 90].map((radius, i) => (
          <motion.circle
            key={radius}
            cx="100"
            cy="100"
            r={radius}
            fill="none"
            stroke="url(#webGradient)"
            strokeWidth="1"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: [0, 0.8, 0] }}
            transition={{
              duration: 1,
              delay: 0.2 + i * 0.1,
              ease: "easeOut"
            }}
          />
        ))}

        {/* Center impact point */}
        <motion.circle
          cx="100"
          cy="100"
          r="3"
          fill="rgba(59, 130, 246, 0.9)"
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: [0, 2, 1], opacity: [1, 0.8, 0] }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        />
      </svg>

      {/* Particle burst */}
      {Array.from({ length: 12 }).map((_, i) => {
        const angle = (i * 30) * (Math.PI / 180);
        const distance = 60 + Math.random() * 40;
        const endX = Math.cos(angle) * distance;
        const endY = Math.sin(angle) * distance;

        return (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-blue-400 rounded-full"
            style={{ left: -2, top: -2 }}
            initial={{ x: 0, y: 0, scale: 0, opacity: 1 }}
            animate={{
              x: endX,
              y: endY,
              scale: [0, 1.5, 0],
              opacity: [1, 0.8, 0]
            }}
            transition={{
              duration: 1.2,
              delay: 0.1 + i * 0.02,
              ease: "easeOut"
            }}
          />
        );
      })}

      {/* Shockwave effect */}
      <motion.div
        className="absolute w-4 h-4 border-2 border-blue-400 rounded-full -translate-x-1/2 -translate-y-1/2"
        initial={{ scale: 0, opacity: 0.8 }}
        animate={{ scale: 20, opacity: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
      />

      {/* Secondary shockwave */}
      <motion.div
        className="absolute w-2 h-2 border border-blue-300 rounded-full -translate-x-1/2 -translate-y-1/2"
        initial={{ scale: 0, opacity: 0.6 }}
        animate={{ scale: 30, opacity: 0 }}
        transition={{ duration: 1.3, delay: 0.1, ease: "easeOut" }}
      />
    </motion.div>
  );
}
