var Jg=Object.defineProperty;var ey=(e,t,n)=>t in e?Jg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ot=(e,t,n)=>ey(e,typeof t!="symbol"?t+"":t,n);function ty(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Ys(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Kd={exports:{}},Xs={},Gd={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gi=Symbol.for("react.element"),ny=Symbol.for("react.portal"),ry=Symbol.for("react.fragment"),iy=Symbol.for("react.strict_mode"),sy=Symbol.for("react.profiler"),oy=Symbol.for("react.provider"),ly=Symbol.for("react.context"),ay=Symbol.for("react.forward_ref"),uy=Symbol.for("react.suspense"),cy=Symbol.for("react.memo"),fy=Symbol.for("react.lazy"),lc=Symbol.iterator;function dy(e){return e===null||typeof e!="object"?null:(e=lc&&e[lc]||e["@@iterator"],typeof e=="function"?e:null)}var Qd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Yd=Object.assign,Xd={};function lr(e,t,n){this.props=e,this.context=t,this.refs=Xd,this.updater=n||Qd}lr.prototype.isReactComponent={};lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Zd(){}Zd.prototype=lr.prototype;function Ma(e,t,n){this.props=e,this.context=t,this.refs=Xd,this.updater=n||Qd}var La=Ma.prototype=new Zd;La.constructor=Ma;Yd(La,lr.prototype);La.isPureReactComponent=!0;var ac=Array.isArray,qd=Object.prototype.hasOwnProperty,Da={current:null},Jd={key:!0,ref:!0,__self:!0,__source:!0};function eh(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)qd.call(t,r)&&!Jd.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:gi,type:e,key:s,ref:o,props:i,_owner:Da.current}}function hy(e,t){return{$$typeof:gi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Na(e){return typeof e=="object"&&e!==null&&e.$$typeof===gi}function py(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var uc=/\/+/g;function Co(e,t){return typeof e=="object"&&e!==null&&e.key!=null?py(""+e.key):t.toString(36)}function Zi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case gi:case ny:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Co(o,0):r,ac(i)?(n="",e!=null&&(n=e.replace(uc,"$&/")+"/"),Zi(i,t,n,"",function(u){return u})):i!=null&&(Na(i)&&(i=hy(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(uc,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",ac(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Co(s,l);o+=Zi(s,t,n,a,i)}else if(a=dy(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Co(s,l++),o+=Zi(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ji(e,t,n){if(e==null)return e;var r=[],i=0;return Zi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function my(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},qi={transition:null},gy={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:qi,ReactCurrentOwner:Da};function th(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:ji,forEach:function(e,t,n){ji(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ji(e,function(){t++}),t},toArray:function(e){return ji(e,function(t){return t})||[]},only:function(e){if(!Na(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=lr;V.Fragment=ry;V.Profiler=sy;V.PureComponent=Ma;V.StrictMode=iy;V.Suspense=uy;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gy;V.act=th;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Yd({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Da.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)qd.call(t,a)&&!Jd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:gi,type:e.type,key:i,ref:s,props:r,_owner:o}};V.createContext=function(e){return e={$$typeof:ly,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:oy,_context:e},e.Consumer=e};V.createElement=eh;V.createFactory=function(e){var t=eh.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:ay,render:e}};V.isValidElement=Na;V.lazy=function(e){return{$$typeof:fy,_payload:{_status:-1,_result:e},_init:my}};V.memo=function(e,t){return{$$typeof:cy,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=qi.transition;qi.transition={};try{e()}finally{qi.transition=t}};V.unstable_act=th;V.useCallback=function(e,t){return xe.current.useCallback(e,t)};V.useContext=function(e){return xe.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};V.useEffect=function(e,t){return xe.current.useEffect(e,t)};V.useId=function(){return xe.current.useId()};V.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return xe.current.useMemo(e,t)};V.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};V.useRef=function(e){return xe.current.useRef(e)};V.useState=function(e){return xe.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return xe.current.useTransition()};V.version="18.3.1";Gd.exports=V;var S=Gd.exports;const tt=Ys(S),yy=ty({__proto__:null,default:tt},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vy=S,xy=Symbol.for("react.element"),wy=Symbol.for("react.fragment"),Sy=Object.prototype.hasOwnProperty,Ty=vy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Cy={key:!0,ref:!0,__self:!0,__source:!0};function nh(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Sy.call(t,r)&&!Cy.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:xy,type:e,key:s,ref:o,props:i,_owner:Ty.current}}Xs.Fragment=wy;Xs.jsx=nh;Xs.jsxs=nh;Kd.exports=Xs;var v=Kd.exports,pl={},rh={exports:{}},De={},ih={exports:{}},sh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,D){var N=R.length;R.push(D);e:for(;0<N;){var X=N-1>>>1,ie=R[X];if(0<i(ie,D))R[X]=D,R[N]=ie,N=X;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var D=R[0],N=R.pop();if(N!==D){R[0]=N;e:for(var X=0,ie=R.length,Ai=ie>>>1;X<Ai;){var Xt=2*(X+1)-1,To=R[Xt],Zt=Xt+1,Ri=R[Zt];if(0>i(To,N))Zt<ie&&0>i(Ri,To)?(R[X]=Ri,R[Zt]=N,X=Zt):(R[X]=To,R[Xt]=N,X=Xt);else if(Zt<ie&&0>i(Ri,N))R[X]=Ri,R[Zt]=N,X=Zt;else break e}}return D}function i(R,D){var N=R.sortIndex-D.sortIndex;return N!==0?N:R.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,f=null,d=3,g=!1,y=!1,x=!1,T=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(R){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=R)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function w(R){if(x=!1,m(R),!y)if(n(a)!==null)y=!0,Ei(C);else{var D=n(u);D!==null&&te(w,D.startTime-R)}}function C(R,D){y=!1,x&&(x=!1,p(P),P=-1),g=!0;var N=d;try{for(m(D),f=n(a);f!==null&&(!(f.expirationTime>D)||R&&!J());){var X=f.callback;if(typeof X=="function"){f.callback=null,d=f.priorityLevel;var ie=X(f.expirationTime<=D);D=e.unstable_now(),typeof ie=="function"?f.callback=ie:f===n(a)&&r(a),m(D)}else r(a);f=n(a)}if(f!==null)var Ai=!0;else{var Xt=n(u);Xt!==null&&te(w,Xt.startTime-D),Ai=!1}return Ai}finally{f=null,d=N,g=!1}}var k=!1,A=null,P=-1,_=5,M=-1;function J(){return!(e.unstable_now()-M<_)}function wt(){if(A!==null){var R=e.unstable_now();M=R;var D=!0;try{D=A(!0,R)}finally{D?Yt():(k=!1,A=null)}}else k=!1}var Yt;if(typeof h=="function")Yt=function(){h(wt)};else if(typeof MessageChannel<"u"){var pr=new MessageChannel,oc=pr.port2;pr.port1.onmessage=wt,Yt=function(){oc.postMessage(null)}}else Yt=function(){T(wt,0)};function Ei(R){A=R,k||(k=!0,Yt())}function te(R,D){P=T(function(){R(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,Ei(C))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(R){switch(d){case 1:case 2:case 3:var D=3;break;default:D=d}var N=d;d=D;try{return R()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,D){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var N=d;d=R;try{return D()}finally{d=N}},e.unstable_scheduleCallback=function(R,D,N){var X=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?X+N:X):N=X,R){case 1:var ie=-1;break;case 2:ie=250;break;case 5:ie=**********;break;case 4:ie=1e4;break;default:ie=5e3}return ie=N+ie,R={id:c++,callback:D,priorityLevel:R,startTime:N,expirationTime:ie,sortIndex:-1},N>X?(R.sortIndex=N,t(u,R),n(a)===null&&R===n(u)&&(x?(p(P),P=-1):x=!0,te(w,N-X))):(R.sortIndex=ie,t(a,R),y||g||(y=!0,Ei(C))),R},e.unstable_shouldYield=J,e.unstable_wrapCallback=function(R){var D=d;return function(){var N=d;d=D;try{return R.apply(this,arguments)}finally{d=N}}}})(sh);ih.exports=sh;var Py=ih.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ky=S,Me=Py;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var oh=new Set,Kr={};function vn(e,t){Xn(e,t),Xn(e+"Capture",t)}function Xn(e,t){for(Kr[e]=t,e=0;e<t.length;e++)oh.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ml=Object.prototype.hasOwnProperty,Ey=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,cc={},fc={};function Ay(e){return ml.call(fc,e)?!0:ml.call(cc,e)?!1:Ey.test(e)?fc[e]=!0:(cc[e]=!0,!1)}function Ry(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function jy(e,t,n,r){if(t===null||typeof t>"u"||Ry(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var _a=/[\-:]([a-z])/g;function Va(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_a,Va);ce[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_a,Va);ce[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_a,Va);ce[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Oa(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(jy(t,n,i,r)&&(n=null),r||i===null?Ay(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=ky.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Mi=Symbol.for("react.element"),kn=Symbol.for("react.portal"),En=Symbol.for("react.fragment"),Ia=Symbol.for("react.strict_mode"),gl=Symbol.for("react.profiler"),lh=Symbol.for("react.provider"),ah=Symbol.for("react.context"),Fa=Symbol.for("react.forward_ref"),yl=Symbol.for("react.suspense"),vl=Symbol.for("react.suspense_list"),za=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),uh=Symbol.for("react.offscreen"),dc=Symbol.iterator;function mr(e){return e===null||typeof e!="object"?null:(e=dc&&e[dc]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Po;function Er(e){if(Po===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Po=t&&t[1]||""}return`
`+Po+e}var ko=!1;function Eo(e,t){if(!e||ko)return"";ko=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{ko=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Er(e):""}function My(e){switch(e.tag){case 5:return Er(e.type);case 16:return Er("Lazy");case 13:return Er("Suspense");case 19:return Er("SuspenseList");case 0:case 2:case 15:return e=Eo(e.type,!1),e;case 11:return e=Eo(e.type.render,!1),e;case 1:return e=Eo(e.type,!0),e;default:return""}}function xl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case En:return"Fragment";case kn:return"Portal";case gl:return"Profiler";case Ia:return"StrictMode";case yl:return"Suspense";case vl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ah:return(e.displayName||"Context")+".Consumer";case lh:return(e._context.displayName||"Context")+".Provider";case Fa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case za:return t=e.displayName||null,t!==null?t:xl(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return xl(e(t))}catch{}}return null}function Ly(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xl(t);case 8:return t===Ia?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ch(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Dy(e){var t=ch(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Li(e){e._valueTracker||(e._valueTracker=Dy(e))}function fh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ch(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ps(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function wl(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function hc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function dh(e,t){t=t.checked,t!=null&&Oa(e,"checked",t,!1)}function Sl(e,t){dh(e,t);var n=Bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Tl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Tl(e,t.type,Bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function pc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Tl(e,t,n){(t!=="number"||ps(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ar=Array.isArray;function $n(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Cl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function mc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(Ar(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Bt(n)}}function hh(e,t){var n=Bt(t.value),r=Bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function gc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ph(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Pl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ph(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Di,mh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Di=Di||document.createElement("div"),Di.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Di.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Gr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ny=["Webkit","ms","Moz","O"];Object.keys(Nr).forEach(function(e){Ny.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nr[t]=Nr[e]})});function gh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nr.hasOwnProperty(e)&&Nr[e]?(""+t).trim():t+"px"}function yh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=gh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var _y=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function kl(e,t){if(t){if(_y[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function El(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Al=null;function Ba(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Rl=null,Wn=null,Hn=null;function yc(e){if(e=xi(e)){if(typeof Rl!="function")throw Error(E(280));var t=e.stateNode;t&&(t=to(t),Rl(e.stateNode,e.type,t))}}function vh(e){Wn?Hn?Hn.push(e):Hn=[e]:Wn=e}function xh(){if(Wn){var e=Wn,t=Hn;if(Hn=Wn=null,yc(e),t)for(e=0;e<t.length;e++)yc(t[e])}}function wh(e,t){return e(t)}function Sh(){}var Ao=!1;function Th(e,t,n){if(Ao)return e(t,n);Ao=!0;try{return wh(e,t,n)}finally{Ao=!1,(Wn!==null||Hn!==null)&&(Sh(),xh())}}function Qr(e,t){var n=e.stateNode;if(n===null)return null;var r=to(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var jl=!1;if(pt)try{var gr={};Object.defineProperty(gr,"passive",{get:function(){jl=!0}}),window.addEventListener("test",gr,gr),window.removeEventListener("test",gr,gr)}catch{jl=!1}function Vy(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var _r=!1,ms=null,gs=!1,Ml=null,Oy={onError:function(e){_r=!0,ms=e}};function Iy(e,t,n,r,i,s,o,l,a){_r=!1,ms=null,Vy.apply(Oy,arguments)}function Fy(e,t,n,r,i,s,o,l,a){if(Iy.apply(this,arguments),_r){if(_r){var u=ms;_r=!1,ms=null}else throw Error(E(198));gs||(gs=!0,Ml=u)}}function xn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ch(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function vc(e){if(xn(e)!==e)throw Error(E(188))}function zy(e){var t=e.alternate;if(!t){if(t=xn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return vc(i),e;if(s===r)return vc(i),t;s=s.sibling}throw Error(E(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function Ph(e){return e=zy(e),e!==null?kh(e):null}function kh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=kh(e);if(t!==null)return t;e=e.sibling}return null}var Eh=Me.unstable_scheduleCallback,xc=Me.unstable_cancelCallback,By=Me.unstable_shouldYield,Uy=Me.unstable_requestPaint,q=Me.unstable_now,$y=Me.unstable_getCurrentPriorityLevel,Ua=Me.unstable_ImmediatePriority,Ah=Me.unstable_UserBlockingPriority,ys=Me.unstable_NormalPriority,Wy=Me.unstable_LowPriority,Rh=Me.unstable_IdlePriority,Zs=null,nt=null;function Hy(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(Zs,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Gy,by=Math.log,Ky=Math.LN2;function Gy(e){return e>>>=0,e===0?32:31-(by(e)/Ky|0)|0}var Ni=64,_i=4194304;function Rr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=Rr(l):(s&=o,s!==0&&(r=Rr(s)))}else o=n&~i,o!==0?r=Rr(o):s!==0&&(r=Rr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function Qy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Yy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Ye(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=Qy(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Ll(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function jh(){var e=Ni;return Ni<<=1,!(Ni&4194240)&&(Ni=64),e}function Ro(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Xy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function $a(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function Mh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Lh,Wa,Dh,Nh,_h,Dl=!1,Vi=[],Lt=null,Dt=null,Nt=null,Yr=new Map,Xr=new Map,kt=[],Zy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function wc(e,t){switch(e){case"focusin":case"focusout":Lt=null;break;case"dragenter":case"dragleave":Dt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":Yr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Xr.delete(t.pointerId)}}function yr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=xi(t),t!==null&&Wa(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function qy(e,t,n,r,i){switch(t){case"focusin":return Lt=yr(Lt,e,t,n,r,i),!0;case"dragenter":return Dt=yr(Dt,e,t,n,r,i),!0;case"mouseover":return Nt=yr(Nt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Yr.set(s,yr(Yr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Xr.set(s,yr(Xr.get(s)||null,e,t,n,r,i)),!0}return!1}function Vh(e){var t=sn(e.target);if(t!==null){var n=xn(t);if(n!==null){if(t=n.tag,t===13){if(t=Ch(n),t!==null){e.blockedOn=t,_h(e.priority,function(){Dh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ji(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Nl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Al=r,n.target.dispatchEvent(r),Al=null}else return t=xi(n),t!==null&&Wa(t),e.blockedOn=n,!1;t.shift()}return!0}function Sc(e,t,n){Ji(e)&&n.delete(t)}function Jy(){Dl=!1,Lt!==null&&Ji(Lt)&&(Lt=null),Dt!==null&&Ji(Dt)&&(Dt=null),Nt!==null&&Ji(Nt)&&(Nt=null),Yr.forEach(Sc),Xr.forEach(Sc)}function vr(e,t){e.blockedOn===t&&(e.blockedOn=null,Dl||(Dl=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,Jy)))}function Zr(e){function t(i){return vr(i,e)}if(0<Vi.length){vr(Vi[0],e);for(var n=1;n<Vi.length;n++){var r=Vi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Lt!==null&&vr(Lt,e),Dt!==null&&vr(Dt,e),Nt!==null&&vr(Nt,e),Yr.forEach(t),Xr.forEach(t),n=0;n<kt.length;n++)r=kt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<kt.length&&(n=kt[0],n.blockedOn===null);)Vh(n),n.blockedOn===null&&kt.shift()}var bn=xt.ReactCurrentBatchConfig,xs=!0;function ev(e,t,n,r){var i=I,s=bn.transition;bn.transition=null;try{I=1,Ha(e,t,n,r)}finally{I=i,bn.transition=s}}function tv(e,t,n,r){var i=I,s=bn.transition;bn.transition=null;try{I=4,Ha(e,t,n,r)}finally{I=i,bn.transition=s}}function Ha(e,t,n,r){if(xs){var i=Nl(e,t,n,r);if(i===null)Fo(e,t,r,ws,n),wc(e,r);else if(qy(i,e,t,n,r))r.stopPropagation();else if(wc(e,r),t&4&&-1<Zy.indexOf(e)){for(;i!==null;){var s=xi(i);if(s!==null&&Lh(s),s=Nl(e,t,n,r),s===null&&Fo(e,t,r,ws,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Fo(e,t,r,null,n)}}var ws=null;function Nl(e,t,n,r){if(ws=null,e=Ba(r),e=sn(e),e!==null)if(t=xn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ch(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ws=e,null}function Oh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch($y()){case Ua:return 1;case Ah:return 4;case ys:case Wy:return 16;case Rh:return 536870912;default:return 16}default:return 16}}var Rt=null,ba=null,es=null;function Ih(){if(es)return es;var e,t=ba,n=t.length,r,i="value"in Rt?Rt.value:Rt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return es=i.slice(e,1<r?1-r:void 0)}function ts(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Oi(){return!0}function Tc(){return!1}function Ne(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Oi:Tc,this.isPropagationStopped=Tc,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Oi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Oi)},persist:function(){},isPersistent:Oi}),t}var ar={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ka=Ne(ar),vi=K({},ar,{view:0,detail:0}),nv=Ne(vi),jo,Mo,xr,qs=K({},vi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ga,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==xr&&(xr&&e.type==="mousemove"?(jo=e.screenX-xr.screenX,Mo=e.screenY-xr.screenY):Mo=jo=0,xr=e),jo)},movementY:function(e){return"movementY"in e?e.movementY:Mo}}),Cc=Ne(qs),rv=K({},qs,{dataTransfer:0}),iv=Ne(rv),sv=K({},vi,{relatedTarget:0}),Lo=Ne(sv),ov=K({},ar,{animationName:0,elapsedTime:0,pseudoElement:0}),lv=Ne(ov),av=K({},ar,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),uv=Ne(av),cv=K({},ar,{data:0}),Pc=Ne(cv),fv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hv[e])?!!t[e]:!1}function Ga(){return pv}var mv=K({},vi,{key:function(e){if(e.key){var t=fv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ts(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ga,charCode:function(e){return e.type==="keypress"?ts(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ts(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),gv=Ne(mv),yv=K({},qs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),kc=Ne(yv),vv=K({},vi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ga}),xv=Ne(vv),wv=K({},ar,{propertyName:0,elapsedTime:0,pseudoElement:0}),Sv=Ne(wv),Tv=K({},qs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Cv=Ne(Tv),Pv=[9,13,27,32],Qa=pt&&"CompositionEvent"in window,Vr=null;pt&&"documentMode"in document&&(Vr=document.documentMode);var kv=pt&&"TextEvent"in window&&!Vr,Fh=pt&&(!Qa||Vr&&8<Vr&&11>=Vr),Ec=" ",Ac=!1;function zh(e,t){switch(e){case"keyup":return Pv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var An=!1;function Ev(e,t){switch(e){case"compositionend":return Bh(t);case"keypress":return t.which!==32?null:(Ac=!0,Ec);case"textInput":return e=t.data,e===Ec&&Ac?null:e;default:return null}}function Av(e,t){if(An)return e==="compositionend"||!Qa&&zh(e,t)?(e=Ih(),es=ba=Rt=null,An=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fh&&t.locale!=="ko"?null:t.data;default:return null}}var Rv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Rv[e.type]:t==="textarea"}function Uh(e,t,n,r){vh(r),t=Ss(t,"onChange"),0<t.length&&(n=new Ka("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Or=null,qr=null;function jv(e){qh(e,0)}function Js(e){var t=Mn(e);if(fh(t))return e}function Mv(e,t){if(e==="change")return t}var $h=!1;if(pt){var Do;if(pt){var No="oninput"in document;if(!No){var jc=document.createElement("div");jc.setAttribute("oninput","return;"),No=typeof jc.oninput=="function"}Do=No}else Do=!1;$h=Do&&(!document.documentMode||9<document.documentMode)}function Mc(){Or&&(Or.detachEvent("onpropertychange",Wh),qr=Or=null)}function Wh(e){if(e.propertyName==="value"&&Js(qr)){var t=[];Uh(t,qr,e,Ba(e)),Th(jv,t)}}function Lv(e,t,n){e==="focusin"?(Mc(),Or=t,qr=n,Or.attachEvent("onpropertychange",Wh)):e==="focusout"&&Mc()}function Dv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Js(qr)}function Nv(e,t){if(e==="click")return Js(t)}function _v(e,t){if(e==="input"||e==="change")return Js(t)}function Vv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:Vv;function Jr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ml.call(t,i)||!Ze(e[i],t[i]))return!1}return!0}function Lc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Dc(e,t){var n=Lc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Lc(n)}}function Hh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Hh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function bh(){for(var e=window,t=ps();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ps(e.document)}return t}function Ya(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ov(e){var t=bh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Hh(n.ownerDocument.documentElement,n)){if(r!==null&&Ya(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Dc(n,s);var o=Dc(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Iv=pt&&"documentMode"in document&&11>=document.documentMode,Rn=null,_l=null,Ir=null,Vl=!1;function Nc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Vl||Rn==null||Rn!==ps(r)||(r=Rn,"selectionStart"in r&&Ya(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ir&&Jr(Ir,r)||(Ir=r,r=Ss(_l,"onSelect"),0<r.length&&(t=new Ka("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Rn)))}function Ii(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var jn={animationend:Ii("Animation","AnimationEnd"),animationiteration:Ii("Animation","AnimationIteration"),animationstart:Ii("Animation","AnimationStart"),transitionend:Ii("Transition","TransitionEnd")},_o={},Kh={};pt&&(Kh=document.createElement("div").style,"AnimationEvent"in window||(delete jn.animationend.animation,delete jn.animationiteration.animation,delete jn.animationstart.animation),"TransitionEvent"in window||delete jn.transitionend.transition);function eo(e){if(_o[e])return _o[e];if(!jn[e])return e;var t=jn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Kh)return _o[e]=t[n];return e}var Gh=eo("animationend"),Qh=eo("animationiteration"),Yh=eo("animationstart"),Xh=eo("transitionend"),Zh=new Map,_c="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ht(e,t){Zh.set(e,t),vn(t,[e])}for(var Vo=0;Vo<_c.length;Vo++){var Oo=_c[Vo],Fv=Oo.toLowerCase(),zv=Oo[0].toUpperCase()+Oo.slice(1);Ht(Fv,"on"+zv)}Ht(Gh,"onAnimationEnd");Ht(Qh,"onAnimationIteration");Ht(Yh,"onAnimationStart");Ht("dblclick","onDoubleClick");Ht("focusin","onFocus");Ht("focusout","onBlur");Ht(Xh,"onTransitionEnd");Xn("onMouseEnter",["mouseout","mouseover"]);Xn("onMouseLeave",["mouseout","mouseover"]);Xn("onPointerEnter",["pointerout","pointerover"]);Xn("onPointerLeave",["pointerout","pointerover"]);vn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));vn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));vn("onBeforeInput",["compositionend","keypress","textInput","paste"]);vn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));vn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));vn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bv=new Set("cancel close invalid load scroll toggle".split(" ").concat(jr));function Vc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Fy(r,t,void 0,e),e.currentTarget=null}function qh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;Vc(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;Vc(i,l,u),s=a}}}if(gs)throw e=Ml,gs=!1,Ml=null,e}function z(e,t){var n=t[Bl];n===void 0&&(n=t[Bl]=new Set);var r=e+"__bubble";n.has(r)||(Jh(t,e,2,!1),n.add(r))}function Io(e,t,n){var r=0;t&&(r|=4),Jh(n,e,r,t)}var Fi="_reactListening"+Math.random().toString(36).slice(2);function ei(e){if(!e[Fi]){e[Fi]=!0,oh.forEach(function(n){n!=="selectionchange"&&(Bv.has(n)||Io(n,!1,e),Io(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Fi]||(t[Fi]=!0,Io("selectionchange",!1,t))}}function Jh(e,t,n,r){switch(Oh(t)){case 1:var i=ev;break;case 4:i=tv;break;default:i=Ha}n=i.bind(null,t,n,e),i=void 0,!jl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Fo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=sn(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}Th(function(){var u=s,c=Ba(n),f=[];e:{var d=Zh.get(e);if(d!==void 0){var g=Ka,y=e;switch(e){case"keypress":if(ts(n)===0)break e;case"keydown":case"keyup":g=gv;break;case"focusin":y="focus",g=Lo;break;case"focusout":y="blur",g=Lo;break;case"beforeblur":case"afterblur":g=Lo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Cc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=iv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=xv;break;case Gh:case Qh:case Yh:g=lv;break;case Xh:g=Sv;break;case"scroll":g=nv;break;case"wheel":g=Cv;break;case"copy":case"cut":case"paste":g=uv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=kc}var x=(t&4)!==0,T=!x&&e==="scroll",p=x?d!==null?d+"Capture":null:d;x=[];for(var h=u,m;h!==null;){m=h;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=Qr(h,p),w!=null&&x.push(ti(h,w,m)))),T)break;h=h.return}0<x.length&&(d=new g(d,y,null,n,c),f.push({event:d,listeners:x}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==Al&&(y=n.relatedTarget||n.fromElement)&&(sn(y)||y[mt]))break e;if((g||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=u,y=y?sn(y):null,y!==null&&(T=xn(y),y!==T||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=u),g!==y)){if(x=Cc,w="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=kc,w="onPointerLeave",p="onPointerEnter",h="pointer"),T=g==null?d:Mn(g),m=y==null?d:Mn(y),d=new x(w,h+"leave",g,n,c),d.target=T,d.relatedTarget=m,w=null,sn(c)===u&&(x=new x(p,h+"enter",y,n,c),x.target=m,x.relatedTarget=T,w=x),T=w,g&&y)t:{for(x=g,p=y,h=0,m=x;m;m=Cn(m))h++;for(m=0,w=p;w;w=Cn(w))m++;for(;0<h-m;)x=Cn(x),h--;for(;0<m-h;)p=Cn(p),m--;for(;h--;){if(x===p||p!==null&&x===p.alternate)break t;x=Cn(x),p=Cn(p)}x=null}else x=null;g!==null&&Oc(f,d,g,x,!1),y!==null&&T!==null&&Oc(f,T,y,x,!0)}}e:{if(d=u?Mn(u):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var C=Mv;else if(Rc(d))if($h)C=_v;else{C=Dv;var k=Lv}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=Nv);if(C&&(C=C(e,u))){Uh(f,C,n,c);break e}k&&k(e,d,u),e==="focusout"&&(k=d._wrapperState)&&k.controlled&&d.type==="number"&&Tl(d,"number",d.value)}switch(k=u?Mn(u):window,e){case"focusin":(Rc(k)||k.contentEditable==="true")&&(Rn=k,_l=u,Ir=null);break;case"focusout":Ir=_l=Rn=null;break;case"mousedown":Vl=!0;break;case"contextmenu":case"mouseup":case"dragend":Vl=!1,Nc(f,n,c);break;case"selectionchange":if(Iv)break;case"keydown":case"keyup":Nc(f,n,c)}var A;if(Qa)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else An?zh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Fh&&n.locale!=="ko"&&(An||P!=="onCompositionStart"?P==="onCompositionEnd"&&An&&(A=Ih()):(Rt=c,ba="value"in Rt?Rt.value:Rt.textContent,An=!0)),k=Ss(u,P),0<k.length&&(P=new Pc(P,e,null,n,c),f.push({event:P,listeners:k}),A?P.data=A:(A=Bh(n),A!==null&&(P.data=A)))),(A=kv?Ev(e,n):Av(e,n))&&(u=Ss(u,"onBeforeInput"),0<u.length&&(c=new Pc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=A))}qh(f,t)})}function ti(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ss(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Qr(e,n),s!=null&&r.unshift(ti(e,s,i)),s=Qr(e,t),s!=null&&r.push(ti(e,s,i))),e=e.return}return r}function Cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Oc(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Qr(n,s),a!=null&&o.unshift(ti(n,a,l))):i||(a=Qr(n,s),a!=null&&o.push(ti(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Uv=/\r\n?/g,$v=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(Uv,`
`).replace($v,"")}function zi(e,t,n){if(t=Ic(t),Ic(e)!==t&&n)throw Error(E(425))}function Ts(){}var Ol=null,Il=null;function Fl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zl=typeof setTimeout=="function"?setTimeout:void 0,Wv=typeof clearTimeout=="function"?clearTimeout:void 0,Fc=typeof Promise=="function"?Promise:void 0,Hv=typeof queueMicrotask=="function"?queueMicrotask:typeof Fc<"u"?function(e){return Fc.resolve(null).then(e).catch(bv)}:zl;function bv(e){setTimeout(function(){throw e})}function zo(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Zr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Zr(t)}function _t(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function zc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ur=Math.random().toString(36).slice(2),et="__reactFiber$"+ur,ni="__reactProps$"+ur,mt="__reactContainer$"+ur,Bl="__reactEvents$"+ur,Kv="__reactListeners$"+ur,Gv="__reactHandles$"+ur;function sn(e){var t=e[et];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[et]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=zc(e);e!==null;){if(n=e[et])return n;e=zc(e)}return t}e=n,n=e.parentNode}return null}function xi(e){return e=e[et]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function to(e){return e[ni]||null}var Ul=[],Ln=-1;function bt(e){return{current:e}}function B(e){0>Ln||(e.current=Ul[Ln],Ul[Ln]=null,Ln--)}function F(e,t){Ln++,Ul[Ln]=e.current,e.current=t}var Ut={},ge=bt(Ut),Ce=bt(!1),hn=Ut;function Zn(e,t){var n=e.type.contextTypes;if(!n)return Ut;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function Cs(){B(Ce),B(ge)}function Bc(e,t,n){if(ge.current!==Ut)throw Error(E(168));F(ge,t),F(Ce,n)}function ep(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(E(108,Ly(e)||"Unknown",i));return K({},n,r)}function Ps(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ut,hn=ge.current,F(ge,e),F(Ce,Ce.current),!0}function Uc(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=ep(e,t,hn),r.__reactInternalMemoizedMergedChildContext=e,B(Ce),B(ge),F(ge,e)):B(Ce),F(Ce,n)}var at=null,no=!1,Bo=!1;function tp(e){at===null?at=[e]:at.push(e)}function Qv(e){no=!0,tp(e)}function Kt(){if(!Bo&&at!==null){Bo=!0;var e=0,t=I;try{var n=at;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}at=null,no=!1}catch(i){throw at!==null&&(at=at.slice(e+1)),Eh(Ua,Kt),i}finally{I=t,Bo=!1}}return null}var Dn=[],Nn=0,ks=null,Es=0,Ie=[],Fe=0,pn=null,ut=1,ct="";function Jt(e,t){Dn[Nn++]=Es,Dn[Nn++]=ks,ks=e,Es=t}function np(e,t,n){Ie[Fe++]=ut,Ie[Fe++]=ct,Ie[Fe++]=pn,pn=e;var r=ut;e=ct;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var s=32-Ye(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,ut=1<<32-Ye(t)+i|n<<i|r,ct=s+e}else ut=1<<s|n<<i|r,ct=e}function Xa(e){e.return!==null&&(Jt(e,1),np(e,1,0))}function Za(e){for(;e===ks;)ks=Dn[--Nn],Dn[Nn]=null,Es=Dn[--Nn],Dn[Nn]=null;for(;e===pn;)pn=Ie[--Fe],Ie[Fe]=null,ct=Ie[--Fe],Ie[Fe]=null,ut=Ie[--Fe],Ie[Fe]=null}var Re=null,Ae=null,$=!1,Ge=null;function rp(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function $c(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Re=e,Ae=_t(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Re=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=pn!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Re=e,Ae=null,!0):!1;default:return!1}}function $l(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Wl(e){if($){var t=Ae;if(t){var n=t;if(!$c(e,t)){if($l(e))throw Error(E(418));t=_t(n.nextSibling);var r=Re;t&&$c(e,t)?rp(r,n):(e.flags=e.flags&-4097|2,$=!1,Re=e)}}else{if($l(e))throw Error(E(418));e.flags=e.flags&-4097|2,$=!1,Re=e}}}function Wc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Re=e}function Bi(e){if(e!==Re)return!1;if(!$)return Wc(e),$=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Fl(e.type,e.memoizedProps)),t&&(t=Ae)){if($l(e))throw ip(),Error(E(418));for(;t;)rp(e,t),t=_t(t.nextSibling)}if(Wc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=_t(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Re?_t(e.stateNode.nextSibling):null;return!0}function ip(){for(var e=Ae;e;)e=_t(e.nextSibling)}function qn(){Ae=Re=null,$=!1}function qa(e){Ge===null?Ge=[e]:Ge.push(e)}var Yv=xt.ReactCurrentBatchConfig;function wr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function Ui(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Hc(e){var t=e._init;return t(e._payload)}function sp(e){function t(p,h){if(e){var m=p.deletions;m===null?(p.deletions=[h],p.flags|=16):m.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function i(p,h){return p=Ft(p,h),p.index=0,p.sibling=null,p}function s(p,h,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<h?(p.flags|=2,h):m):(p.flags|=2,h)):(p.flags|=1048576,h)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,h,m,w){return h===null||h.tag!==6?(h=Go(m,p.mode,w),h.return=p,h):(h=i(h,m),h.return=p,h)}function a(p,h,m,w){var C=m.type;return C===En?c(p,h,m.props.children,w,m.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ct&&Hc(C)===h.type)?(w=i(h,m.props),w.ref=wr(p,h,m),w.return=p,w):(w=as(m.type,m.key,m.props,null,p.mode,w),w.ref=wr(p,h,m),w.return=p,w)}function u(p,h,m,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=Qo(m,p.mode,w),h.return=p,h):(h=i(h,m.children||[]),h.return=p,h)}function c(p,h,m,w,C){return h===null||h.tag!==7?(h=fn(m,p.mode,w,C),h.return=p,h):(h=i(h,m),h.return=p,h)}function f(p,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Go(""+h,p.mode,m),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Mi:return m=as(h.type,h.key,h.props,null,p.mode,m),m.ref=wr(p,null,h),m.return=p,m;case kn:return h=Qo(h,p.mode,m),h.return=p,h;case Ct:var w=h._init;return f(p,w(h._payload),m)}if(Ar(h)||mr(h))return h=fn(h,p.mode,m,null),h.return=p,h;Ui(p,h)}return null}function d(p,h,m,w){var C=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:l(p,h,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Mi:return m.key===C?a(p,h,m,w):null;case kn:return m.key===C?u(p,h,m,w):null;case Ct:return C=m._init,d(p,h,C(m._payload),w)}if(Ar(m)||mr(m))return C!==null?null:c(p,h,m,w,null);Ui(p,m)}return null}function g(p,h,m,w,C){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,l(h,p,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Mi:return p=p.get(w.key===null?m:w.key)||null,a(h,p,w,C);case kn:return p=p.get(w.key===null?m:w.key)||null,u(h,p,w,C);case Ct:var k=w._init;return g(p,h,m,k(w._payload),C)}if(Ar(w)||mr(w))return p=p.get(m)||null,c(h,p,w,C,null);Ui(h,w)}return null}function y(p,h,m,w){for(var C=null,k=null,A=h,P=h=0,_=null;A!==null&&P<m.length;P++){A.index>P?(_=A,A=null):_=A.sibling;var M=d(p,A,m[P],w);if(M===null){A===null&&(A=_);break}e&&A&&M.alternate===null&&t(p,A),h=s(M,h,P),k===null?C=M:k.sibling=M,k=M,A=_}if(P===m.length)return n(p,A),$&&Jt(p,P),C;if(A===null){for(;P<m.length;P++)A=f(p,m[P],w),A!==null&&(h=s(A,h,P),k===null?C=A:k.sibling=A,k=A);return $&&Jt(p,P),C}for(A=r(p,A);P<m.length;P++)_=g(A,p,P,m[P],w),_!==null&&(e&&_.alternate!==null&&A.delete(_.key===null?P:_.key),h=s(_,h,P),k===null?C=_:k.sibling=_,k=_);return e&&A.forEach(function(J){return t(p,J)}),$&&Jt(p,P),C}function x(p,h,m,w){var C=mr(m);if(typeof C!="function")throw Error(E(150));if(m=C.call(m),m==null)throw Error(E(151));for(var k=C=null,A=h,P=h=0,_=null,M=m.next();A!==null&&!M.done;P++,M=m.next()){A.index>P?(_=A,A=null):_=A.sibling;var J=d(p,A,M.value,w);if(J===null){A===null&&(A=_);break}e&&A&&J.alternate===null&&t(p,A),h=s(J,h,P),k===null?C=J:k.sibling=J,k=J,A=_}if(M.done)return n(p,A),$&&Jt(p,P),C;if(A===null){for(;!M.done;P++,M=m.next())M=f(p,M.value,w),M!==null&&(h=s(M,h,P),k===null?C=M:k.sibling=M,k=M);return $&&Jt(p,P),C}for(A=r(p,A);!M.done;P++,M=m.next())M=g(A,p,P,M.value,w),M!==null&&(e&&M.alternate!==null&&A.delete(M.key===null?P:M.key),h=s(M,h,P),k===null?C=M:k.sibling=M,k=M);return e&&A.forEach(function(wt){return t(p,wt)}),$&&Jt(p,P),C}function T(p,h,m,w){if(typeof m=="object"&&m!==null&&m.type===En&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Mi:e:{for(var C=m.key,k=h;k!==null;){if(k.key===C){if(C=m.type,C===En){if(k.tag===7){n(p,k.sibling),h=i(k,m.props.children),h.return=p,p=h;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Ct&&Hc(C)===k.type){n(p,k.sibling),h=i(k,m.props),h.ref=wr(p,k,m),h.return=p,p=h;break e}n(p,k);break}else t(p,k);k=k.sibling}m.type===En?(h=fn(m.props.children,p.mode,w,m.key),h.return=p,p=h):(w=as(m.type,m.key,m.props,null,p.mode,w),w.ref=wr(p,h,m),w.return=p,p=w)}return o(p);case kn:e:{for(k=m.key;h!==null;){if(h.key===k)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(p,h.sibling),h=i(h,m.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=Qo(m,p.mode,w),h.return=p,p=h}return o(p);case Ct:return k=m._init,T(p,h,k(m._payload),w)}if(Ar(m))return y(p,h,m,w);if(mr(m))return x(p,h,m,w);Ui(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(p,h.sibling),h=i(h,m),h.return=p,p=h):(n(p,h),h=Go(m,p.mode,w),h.return=p,p=h),o(p)):n(p,h)}return T}var Jn=sp(!0),op=sp(!1),As=bt(null),Rs=null,_n=null,Ja=null;function eu(){Ja=_n=Rs=null}function tu(e){var t=As.current;B(As),e._currentValue=t}function Hl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Kn(e,t){Rs=e,Ja=_n=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Te=!0),e.firstContext=null)}function Ue(e){var t=e._currentValue;if(Ja!==e)if(e={context:e,memoizedValue:t,next:null},_n===null){if(Rs===null)throw Error(E(308));_n=e,Rs.dependencies={lanes:0,firstContext:e}}else _n=_n.next=e;return t}var on=null;function nu(e){on===null?on=[e]:on.push(e)}function lp(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,nu(t)):(n.next=i.next,i.next=n),t.interleaved=n,gt(e,r)}function gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Pt=!1;function ru(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ap(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,gt(e,n)}return i=r.interleaved,i===null?(t.next=t,nu(r)):(t.next=i.next,i.next=t),r.interleaved=t,gt(e,n)}function ns(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,$a(e,n)}}function bc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function js(e,t,n,r){var i=e.updateQueue;Pt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var f=i.baseState;o=0,c=u=a=null,l=s;do{var d=l.lane,g=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,x=l;switch(d=t,g=n,x.tag){case 1:if(y=x.payload,typeof y=="function"){f=y.call(g,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=x.payload,d=typeof y=="function"?y.call(g,f,d):y,d==null)break e;f=K({},f,d);break e;case 2:Pt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else g={eventTime:g,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=f):c=c.next=g,o|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);gn|=o,e.lanes=o,e.memoizedState=f}}function Kc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(E(191,i));i.call(r)}}}var wi={},rt=bt(wi),ri=bt(wi),ii=bt(wi);function ln(e){if(e===wi)throw Error(E(174));return e}function iu(e,t){switch(F(ii,t),F(ri,e),F(rt,wi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Pl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Pl(t,e)}B(rt),F(rt,t)}function er(){B(rt),B(ri),B(ii)}function up(e){ln(ii.current);var t=ln(rt.current),n=Pl(t,e.type);t!==n&&(F(ri,e),F(rt,n))}function su(e){ri.current===e&&(B(rt),B(ri))}var W=bt(0);function Ms(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Uo=[];function ou(){for(var e=0;e<Uo.length;e++)Uo[e]._workInProgressVersionPrimary=null;Uo.length=0}var rs=xt.ReactCurrentDispatcher,$o=xt.ReactCurrentBatchConfig,mn=0,b=null,ne=null,se=null,Ls=!1,Fr=!1,si=0,Xv=0;function fe(){throw Error(E(321))}function lu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function au(e,t,n,r,i,s){if(mn=s,b=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,rs.current=e===null||e.memoizedState===null?e0:t0,e=n(r,i),Fr){s=0;do{if(Fr=!1,si=0,25<=s)throw Error(E(301));s+=1,se=ne=null,t.updateQueue=null,rs.current=n0,e=n(r,i)}while(Fr)}if(rs.current=Ds,t=ne!==null&&ne.next!==null,mn=0,se=ne=b=null,Ls=!1,t)throw Error(E(300));return e}function uu(){var e=si!==0;return si=0,e}function Je(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?b.memoizedState=se=e:se=se.next=e,se}function $e(){if(ne===null){var e=b.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=se===null?b.memoizedState:se.next;if(t!==null)se=t,ne=e;else{if(e===null)throw Error(E(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},se===null?b.memoizedState=se=e:se=se.next=e}return se}function oi(e,t){return typeof t=="function"?t(e):t}function Wo(e){var t=$e(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=ne,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((mn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,o=r):a=a.next=f,b.lanes|=c,gn|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,Ze(r,t.memoizedState)||(Te=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,b.lanes|=s,gn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ho(e){var t=$e(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);Ze(s,t.memoizedState)||(Te=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function cp(){}function fp(e,t){var n=b,r=$e(),i=t(),s=!Ze(r.memoizedState,i);if(s&&(r.memoizedState=i,Te=!0),r=r.queue,cu(pp.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,li(9,hp.bind(null,n,r,i,t),void 0,null),oe===null)throw Error(E(349));mn&30||dp(n,t,i)}return i}function dp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=b.updateQueue,t===null?(t={lastEffect:null,stores:null},b.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function hp(e,t,n,r){t.value=n,t.getSnapshot=r,mp(t)&&gp(e)}function pp(e,t,n){return n(function(){mp(t)&&gp(e)})}function mp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function gp(e){var t=gt(e,1);t!==null&&Xe(t,e,1,-1)}function Gc(e){var t=Je();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=Jv.bind(null,b,e),[t.memoizedState,e]}function li(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=b.updateQueue,t===null?(t={lastEffect:null,stores:null},b.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function yp(){return $e().memoizedState}function is(e,t,n,r){var i=Je();b.flags|=e,i.memoizedState=li(1|t,n,void 0,r===void 0?null:r)}function ro(e,t,n,r){var i=$e();r=r===void 0?null:r;var s=void 0;if(ne!==null){var o=ne.memoizedState;if(s=o.destroy,r!==null&&lu(r,o.deps)){i.memoizedState=li(t,n,s,r);return}}b.flags|=e,i.memoizedState=li(1|t,n,s,r)}function Qc(e,t){return is(8390656,8,e,t)}function cu(e,t){return ro(2048,8,e,t)}function vp(e,t){return ro(4,2,e,t)}function xp(e,t){return ro(4,4,e,t)}function wp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sp(e,t,n){return n=n!=null?n.concat([e]):null,ro(4,4,wp.bind(null,t,e),n)}function fu(){}function Tp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&lu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Cp(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&lu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Pp(e,t,n){return mn&21?(Ze(n,t)||(n=jh(),b.lanes|=n,gn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Te=!0),e.memoizedState=n)}function Zv(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=$o.transition;$o.transition={};try{e(!1),t()}finally{I=n,$o.transition=r}}function kp(){return $e().memoizedState}function qv(e,t,n){var r=It(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ep(e))Ap(t,n);else if(n=lp(e,t,n,r),n!==null){var i=ve();Xe(n,e,r,i),Rp(n,t,r)}}function Jv(e,t,n){var r=It(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ep(e))Ap(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,Ze(l,o)){var a=t.interleaved;a===null?(i.next=i,nu(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=lp(e,t,i,r),n!==null&&(i=ve(),Xe(n,e,r,i),Rp(n,t,r))}}function Ep(e){var t=e.alternate;return e===b||t!==null&&t===b}function Ap(e,t){Fr=Ls=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Rp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,$a(e,n)}}var Ds={readContext:Ue,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},e0={readContext:Ue,useCallback:function(e,t){return Je().memoizedState=[e,t===void 0?null:t],e},useContext:Ue,useEffect:Qc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,is(4194308,4,wp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return is(4194308,4,e,t)},useInsertionEffect:function(e,t){return is(4,2,e,t)},useMemo:function(e,t){var n=Je();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Je();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qv.bind(null,b,e),[r.memoizedState,e]},useRef:function(e){var t=Je();return e={current:e},t.memoizedState=e},useState:Gc,useDebugValue:fu,useDeferredValue:function(e){return Je().memoizedState=e},useTransition:function(){var e=Gc(!1),t=e[0];return e=Zv.bind(null,e[1]),Je().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=b,i=Je();if($){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),oe===null)throw Error(E(349));mn&30||dp(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Qc(pp.bind(null,r,s,e),[e]),r.flags|=2048,li(9,hp.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Je(),t=oe.identifierPrefix;if($){var n=ct,r=ut;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=si++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},t0={readContext:Ue,useCallback:Tp,useContext:Ue,useEffect:cu,useImperativeHandle:Sp,useInsertionEffect:vp,useLayoutEffect:xp,useMemo:Cp,useReducer:Wo,useRef:yp,useState:function(){return Wo(oi)},useDebugValue:fu,useDeferredValue:function(e){var t=$e();return Pp(t,ne.memoizedState,e)},useTransition:function(){var e=Wo(oi)[0],t=$e().memoizedState;return[e,t]},useMutableSource:cp,useSyncExternalStore:fp,useId:kp,unstable_isNewReconciler:!1},n0={readContext:Ue,useCallback:Tp,useContext:Ue,useEffect:cu,useImperativeHandle:Sp,useInsertionEffect:vp,useLayoutEffect:xp,useMemo:Cp,useReducer:Ho,useRef:yp,useState:function(){return Ho(oi)},useDebugValue:fu,useDeferredValue:function(e){var t=$e();return ne===null?t.memoizedState=e:Pp(t,ne.memoizedState,e)},useTransition:function(){var e=Ho(oi)[0],t=$e().memoizedState;return[e,t]},useMutableSource:cp,useSyncExternalStore:fp,useId:kp,unstable_isNewReconciler:!1};function be(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function bl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var io={isMounted:function(e){return(e=e._reactInternals)?xn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ve(),i=It(e),s=ft(r,i);s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(Xe(t,e,i,r),ns(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ve(),i=It(e),s=ft(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(Xe(t,e,i,r),ns(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ve(),r=It(e),i=ft(n,r);i.tag=2,t!=null&&(i.callback=t),t=Vt(e,i,r),t!==null&&(Xe(t,e,r,n),ns(t,e,r))}};function Yc(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Jr(n,r)||!Jr(i,s):!0}function jp(e,t,n){var r=!1,i=Ut,s=t.contextType;return typeof s=="object"&&s!==null?s=Ue(s):(i=Pe(t)?hn:ge.current,r=t.contextTypes,s=(r=r!=null)?Zn(e,i):Ut),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=io,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Xc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&io.enqueueReplaceState(t,t.state,null)}function Kl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},ru(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ue(s):(s=Pe(t)?hn:ge.current,i.context=Zn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(bl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&io.enqueueReplaceState(i,i.state,null),js(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function tr(e,t){try{var n="",r=t;do n+=My(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function bo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Gl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var r0=typeof WeakMap=="function"?WeakMap:Map;function Mp(e,t,n){n=ft(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){_s||(_s=!0,ra=r),Gl(e,t)},n}function Lp(e,t,n){n=ft(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Gl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Gl(e,t),typeof r!="function"&&(Ot===null?Ot=new Set([this]):Ot.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Zc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new r0;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=y0.bind(null,e,t,n),t.then(e,e))}function qc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Jc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ft(-1,1),t.tag=2,Vt(n,t,1))),n.lanes|=1),e)}var i0=xt.ReactCurrentOwner,Te=!1;function ye(e,t,n,r){t.child=e===null?op(t,null,n,r):Jn(t,e.child,n,r)}function ef(e,t,n,r,i){n=n.render;var s=t.ref;return Kn(t,i),r=au(e,t,n,r,s,i),n=uu(),e!==null&&!Te?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):($&&n&&Xa(t),t.flags|=1,ye(e,t,r,i),t.child)}function tf(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!xu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Dp(e,t,s,r,i)):(e=as(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Jr,n(o,r)&&e.ref===t.ref)return yt(e,t,i)}return t.flags|=1,e=Ft(s,r),e.ref=t.ref,e.return=t,t.child=e}function Dp(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Jr(s,r)&&e.ref===t.ref)if(Te=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Te=!0);else return t.lanes=e.lanes,yt(e,t,i)}return Ql(e,t,n,r,i)}function Np(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(On,Ee),Ee|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(On,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,F(On,Ee),Ee|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,F(On,Ee),Ee|=r;return ye(e,t,i,n),t.child}function _p(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ql(e,t,n,r,i){var s=Pe(n)?hn:ge.current;return s=Zn(t,s),Kn(t,i),n=au(e,t,n,r,s,i),r=uu(),e!==null&&!Te?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):($&&r&&Xa(t),t.flags|=1,ye(e,t,n,i),t.child)}function nf(e,t,n,r,i){if(Pe(n)){var s=!0;Ps(t)}else s=!1;if(Kn(t,i),t.stateNode===null)ss(e,t),jp(t,n,r),Kl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ue(u):(u=Pe(n)?hn:ge.current,u=Zn(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Xc(t,o,r,u),Pt=!1;var d=t.memoizedState;o.state=d,js(t,r,o,i),a=t.memoizedState,l!==r||d!==a||Ce.current||Pt?(typeof c=="function"&&(bl(t,n,c,r),a=t.memoizedState),(l=Pt||Yc(t,n,l,r,d,a,u))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ap(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:be(t.type,l),o.props=u,f=t.pendingProps,d=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ue(a):(a=Pe(n)?hn:ge.current,a=Zn(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==f||d!==a)&&Xc(t,o,r,a),Pt=!1,d=t.memoizedState,o.state=d,js(t,r,o,i);var y=t.memoizedState;l!==f||d!==y||Ce.current||Pt?(typeof g=="function"&&(bl(t,n,g,r),y=t.memoizedState),(u=Pt||Yc(t,n,u,r,d,y,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,y,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,y,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),o.props=r,o.state=y,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return Yl(e,t,n,r,s,i)}function Yl(e,t,n,r,i,s){_p(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Uc(t,n,!1),yt(e,t,s);r=t.stateNode,i0.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Jn(t,e.child,null,s),t.child=Jn(t,null,l,s)):ye(e,t,l,s),t.memoizedState=r.state,i&&Uc(t,n,!0),t.child}function Vp(e){var t=e.stateNode;t.pendingContext?Bc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Bc(e,t.context,!1),iu(e,t.containerInfo)}function rf(e,t,n,r,i){return qn(),qa(i),t.flags|=256,ye(e,t,n,r),t.child}var Xl={dehydrated:null,treeContext:null,retryLane:0};function Zl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Op(e,t,n){var r=t.pendingProps,i=W.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),F(W,i&1),e===null)return Wl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=lo(o,r,0,null),e=fn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Zl(n),t.memoizedState=Xl,e):du(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return s0(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ft(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Ft(l,s):(s=fn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Zl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Xl,r}return s=e.child,e=s.sibling,r=Ft(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function du(e,t){return t=lo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function $i(e,t,n,r){return r!==null&&qa(r),Jn(t,e.child,null,n),e=du(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function s0(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=bo(Error(E(422))),$i(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=lo({mode:"visible",children:r.children},i,0,null),s=fn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Jn(t,e.child,null,o),t.child.memoizedState=Zl(o),t.memoizedState=Xl,s);if(!(t.mode&1))return $i(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(E(419)),r=bo(s,r,void 0),$i(e,t,o,r)}if(l=(o&e.childLanes)!==0,Te||l){if(r=oe,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,gt(e,i),Xe(r,e,i,-1))}return vu(),r=bo(Error(E(421))),$i(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=v0.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Ae=_t(i.nextSibling),Re=t,$=!0,Ge=null,e!==null&&(Ie[Fe++]=ut,Ie[Fe++]=ct,Ie[Fe++]=pn,ut=e.id,ct=e.overflow,pn=t),t=du(t,r.children),t.flags|=4096,t)}function sf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Hl(e.return,t,n)}function Ko(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Ip(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(ye(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sf(e,n,t);else if(e.tag===19)sf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(W,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Ms(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ko(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Ms(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ko(t,!0,n,null,s);break;case"together":Ko(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ss(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),gn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function o0(e,t,n){switch(t.tag){case 3:Vp(t),qn();break;case 5:up(t);break;case 1:Pe(t.type)&&Ps(t);break;case 4:iu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;F(As,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?Op(e,t,n):(F(W,W.current&1),e=yt(e,t,n),e!==null?e.sibling:null);F(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ip(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,Np(e,t,n)}return yt(e,t,n)}var Fp,ql,zp,Bp;Fp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ql=function(){};zp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,ln(rt.current);var s=null;switch(n){case"input":i=wl(e,i),r=wl(e,r),s=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),s=[];break;case"textarea":i=Cl(e,i),r=Cl(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ts)}kl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Kr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Kr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&z("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Bp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Sr(e,t){if(!$)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function l0(e,t,n){var r=t.pendingProps;switch(Za(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Pe(t.type)&&Cs(),de(t),null;case 3:return r=t.stateNode,er(),B(Ce),B(ge),ou(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Bi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ge!==null&&(oa(Ge),Ge=null))),ql(e,t),de(t),null;case 5:su(t);var i=ln(ii.current);if(n=t.type,e!==null&&t.stateNode!=null)zp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return de(t),null}if(e=ln(rt.current),Bi(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[et]=t,r[ni]=s,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(i=0;i<jr.length;i++)z(jr[i],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":hc(r,s),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},z("invalid",r);break;case"textarea":mc(r,s),z("invalid",r)}kl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&zi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&zi(r.textContent,l,e),i=["children",""+l]):Kr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&z("scroll",r)}switch(n){case"input":Li(r),pc(r,s,!0);break;case"textarea":Li(r),gc(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Ts)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ph(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[et]=t,e[ni]=r,Fp(e,t,!1,!1),t.stateNode=e;e:{switch(o=El(n,r),n){case"dialog":z("cancel",e),z("close",e),i=r;break;case"iframe":case"object":case"embed":z("load",e),i=r;break;case"video":case"audio":for(i=0;i<jr.length;i++)z(jr[i],e);i=r;break;case"source":z("error",e),i=r;break;case"img":case"image":case"link":z("error",e),z("load",e),i=r;break;case"details":z("toggle",e),i=r;break;case"input":hc(e,r),i=wl(e,r),z("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),z("invalid",e);break;case"textarea":mc(e,r),i=Cl(e,r),z("invalid",e);break;default:i=r}kl(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?yh(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&mh(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Gr(e,a):typeof a=="number"&&Gr(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Kr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&z("scroll",e):a!=null&&Oa(e,s,a,o))}switch(n){case"input":Li(e),pc(e,r,!1);break;case"textarea":Li(e),gc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Bt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?$n(e,!!r.multiple,s,!1):r.defaultValue!=null&&$n(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ts)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Bp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=ln(ii.current),ln(rt.current),Bi(t)){if(r=t.stateNode,n=t.memoizedProps,r[et]=t,(s=r.nodeValue!==n)&&(e=Re,e!==null))switch(e.tag){case 3:zi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&zi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[et]=t,t.stateNode=r}return de(t),null;case 13:if(B(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if($&&Ae!==null&&t.mode&1&&!(t.flags&128))ip(),qn(),t.flags|=98560,s=!1;else if(s=Bi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(E(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(E(317));s[et]=t}else qn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),s=!1}else Ge!==null&&(oa(Ge),Ge=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?re===0&&(re=3):vu())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return er(),ql(e,t),e===null&&ei(t.stateNode.containerInfo),de(t),null;case 10:return tu(t.type._context),de(t),null;case 17:return Pe(t.type)&&Cs(),de(t),null;case 19:if(B(W),s=t.memoizedState,s===null)return de(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)Sr(s,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ms(e),o!==null){for(t.flags|=128,Sr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(W,W.current&1|2),t.child}e=e.sibling}s.tail!==null&&q()>nr&&(t.flags|=128,r=!0,Sr(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ms(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Sr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!$)return de(t),null}else 2*q()-s.renderingStartTime>nr&&n!==1073741824&&(t.flags|=128,r=!0,Sr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=q(),t.sibling=null,n=W.current,F(W,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return yu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function a0(e,t){switch(Za(t),t.tag){case 1:return Pe(t.type)&&Cs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return er(),B(Ce),B(ge),ou(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return su(t),null;case 13:if(B(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));qn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(W),null;case 4:return er(),null;case 10:return tu(t.type._context),null;case 22:case 23:return yu(),null;case 24:return null;default:return null}}var Wi=!1,pe=!1,u0=typeof WeakSet=="function"?WeakSet:Set,j=null;function Vn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function Jl(e,t,n){try{n()}catch(r){Q(e,t,r)}}var of=!1;function c0(e,t){if(Ol=xs,e=bh(),Ya(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var g;f!==n||i!==0&&f.nodeType!==3||(l=o+i),f!==s||r!==0&&f.nodeType!==3||(a=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(g=f.firstChild)!==null;)d=f,f=g;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=o),d===s&&++c===r&&(a=o),(g=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Il={focusedElem:e,selectionRange:n},xs=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var x=y.memoizedProps,T=y.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?x:be(t.type,x),T);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(w){Q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return y=of,of=!1,y}function zr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Jl(t,n,s)}i=i.next}while(i!==r)}}function so(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ea(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Up(e){var t=e.alternate;t!==null&&(e.alternate=null,Up(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[et],delete t[ni],delete t[Bl],delete t[Kv],delete t[Gv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function $p(e){return e.tag===5||e.tag===3||e.tag===4}function lf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||$p(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ta(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ts));else if(r!==4&&(e=e.child,e!==null))for(ta(e,t,n),e=e.sibling;e!==null;)ta(e,t,n),e=e.sibling}function na(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(na(e,t,n),e=e.sibling;e!==null;)na(e,t,n),e=e.sibling}var le=null,Ke=!1;function St(e,t,n){for(n=n.child;n!==null;)Wp(e,t,n),n=n.sibling}function Wp(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(Zs,n)}catch{}switch(n.tag){case 5:pe||Vn(n,t);case 6:var r=le,i=Ke;le=null,St(e,t,n),le=r,Ke=i,le!==null&&(Ke?(e=le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):le.removeChild(n.stateNode));break;case 18:le!==null&&(Ke?(e=le,n=n.stateNode,e.nodeType===8?zo(e.parentNode,n):e.nodeType===1&&zo(e,n),Zr(e)):zo(le,n.stateNode));break;case 4:r=le,i=Ke,le=n.stateNode.containerInfo,Ke=!0,St(e,t,n),le=r,Ke=i;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&Jl(n,t,o),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!pe&&(Vn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Q(n,t,l)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,St(e,t,n),pe=r):St(e,t,n);break;default:St(e,t,n)}}function af(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new u0),t.forEach(function(r){var i=x0.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:le=l.stateNode,Ke=!1;break e;case 3:le=l.stateNode.containerInfo,Ke=!0;break e;case 4:le=l.stateNode.containerInfo,Ke=!0;break e}l=l.return}if(le===null)throw Error(E(160));Wp(s,o,i),le=null,Ke=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Hp(t,e),t=t.sibling}function Hp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),qe(e),r&4){try{zr(3,e,e.return),so(3,e)}catch(x){Q(e,e.return,x)}try{zr(5,e,e.return)}catch(x){Q(e,e.return,x)}}break;case 1:We(t,e),qe(e),r&512&&n!==null&&Vn(n,n.return);break;case 5:if(We(t,e),qe(e),r&512&&n!==null&&Vn(n,n.return),e.flags&32){var i=e.stateNode;try{Gr(i,"")}catch(x){Q(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&dh(i,s),El(l,o);var u=El(l,s);for(o=0;o<a.length;o+=2){var c=a[o],f=a[o+1];c==="style"?yh(i,f):c==="dangerouslySetInnerHTML"?mh(i,f):c==="children"?Gr(i,f):Oa(i,c,f,u)}switch(l){case"input":Sl(i,s);break;case"textarea":hh(i,s);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?$n(i,!!s.multiple,g,!1):d!==!!s.multiple&&(s.defaultValue!=null?$n(i,!!s.multiple,s.defaultValue,!0):$n(i,!!s.multiple,s.multiple?[]:"",!1))}i[ni]=s}catch(x){Q(e,e.return,x)}}break;case 6:if(We(t,e),qe(e),r&4){if(e.stateNode===null)throw Error(E(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){Q(e,e.return,x)}}break;case 3:if(We(t,e),qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Zr(t.containerInfo)}catch(x){Q(e,e.return,x)}break;case 4:We(t,e),qe(e);break;case 13:We(t,e),qe(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(mu=q())),r&4&&af(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(u=pe)||c,We(t,e),pe=u):We(t,e),qe(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(j=e,c=e.child;c!==null;){for(f=j=c;j!==null;){switch(d=j,g=d.child,d.tag){case 0:case 11:case 14:case 15:zr(4,d,d.return);break;case 1:Vn(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(x){Q(r,n,x)}}break;case 5:Vn(d,d.return);break;case 22:if(d.memoizedState!==null){cf(f);continue}}g!==null?(g.return=d,j=g):cf(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,a=f.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=gh("display",o))}catch(x){Q(e,e.return,x)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(x){Q(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:We(t,e),qe(e),r&4&&af(e);break;case 21:break;default:We(t,e),qe(e)}}function qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if($p(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Gr(i,""),r.flags&=-33);var s=lf(e);na(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=lf(e);ta(e,l,o);break;default:throw Error(E(161))}}catch(a){Q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function f0(e,t,n){j=e,bp(e)}function bp(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Wi;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||pe;l=Wi;var u=pe;if(Wi=o,(pe=a)&&!u)for(j=i;j!==null;)o=j,a=o.child,o.tag===22&&o.memoizedState!==null?ff(i):a!==null?(a.return=o,j=a):ff(i);for(;s!==null;)j=s,bp(s),s=s.sibling;j=i,Wi=l,pe=u}uf(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,j=s):uf(e)}}function uf(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||so(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:be(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Kc(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Kc(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Zr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}pe||t.flags&512&&ea(t)}catch(d){Q(t,t.return,d)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function cf(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function ff(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{so(4,t)}catch(a){Q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Q(t,i,a)}}var s=t.return;try{ea(t)}catch(a){Q(t,s,a)}break;case 5:var o=t.return;try{ea(t)}catch(a){Q(t,o,a)}}}catch(a){Q(t,t.return,a)}if(t===e){j=null;break}var l=t.sibling;if(l!==null){l.return=t.return,j=l;break}j=t.return}}var d0=Math.ceil,Ns=xt.ReactCurrentDispatcher,hu=xt.ReactCurrentOwner,Be=xt.ReactCurrentBatchConfig,O=0,oe=null,ee=null,ue=0,Ee=0,On=bt(0),re=0,ai=null,gn=0,oo=0,pu=0,Br=null,Se=null,mu=0,nr=1/0,lt=null,_s=!1,ra=null,Ot=null,Hi=!1,jt=null,Vs=0,Ur=0,ia=null,os=-1,ls=0;function ve(){return O&6?q():os!==-1?os:os=q()}function It(e){return e.mode&1?O&2&&ue!==0?ue&-ue:Yv.transition!==null?(ls===0&&(ls=jh()),ls):(e=I,e!==0||(e=window.event,e=e===void 0?16:Oh(e.type)),e):1}function Xe(e,t,n,r){if(50<Ur)throw Ur=0,ia=null,Error(E(185));yi(e,n,r),(!(O&2)||e!==oe)&&(e===oe&&(!(O&2)&&(oo|=n),re===4&&Et(e,ue)),ke(e,r),n===1&&O===0&&!(t.mode&1)&&(nr=q()+500,no&&Kt()))}function ke(e,t){var n=e.callbackNode;Yy(e,t);var r=vs(e,e===oe?ue:0);if(r===0)n!==null&&xc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&xc(n),t===1)e.tag===0?Qv(df.bind(null,e)):tp(df.bind(null,e)),Hv(function(){!(O&6)&&Kt()}),n=null;else{switch(Mh(r)){case 1:n=Ua;break;case 4:n=Ah;break;case 16:n=ys;break;case 536870912:n=Rh;break;default:n=ys}n=Jp(n,Kp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Kp(e,t){if(os=-1,ls=0,O&6)throw Error(E(327));var n=e.callbackNode;if(Gn()&&e.callbackNode!==n)return null;var r=vs(e,e===oe?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Os(e,r);else{t=r;var i=O;O|=2;var s=Qp();(oe!==e||ue!==t)&&(lt=null,nr=q()+500,cn(e,t));do try{m0();break}catch(l){Gp(e,l)}while(!0);eu(),Ns.current=s,O=i,ee!==null?t=0:(oe=null,ue=0,t=re)}if(t!==0){if(t===2&&(i=Ll(e),i!==0&&(r=i,t=sa(e,i))),t===1)throw n=ai,cn(e,0),Et(e,r),ke(e,q()),n;if(t===6)Et(e,r);else{if(i=e.current.alternate,!(r&30)&&!h0(i)&&(t=Os(e,r),t===2&&(s=Ll(e),s!==0&&(r=s,t=sa(e,s))),t===1))throw n=ai,cn(e,0),Et(e,r),ke(e,q()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:en(e,Se,lt);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=mu+500-q(),10<t)){if(vs(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ve(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=zl(en.bind(null,e,Se,lt),t);break}en(e,Se,lt);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Ye(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*d0(r/1960))-r,10<r){e.timeoutHandle=zl(en.bind(null,e,Se,lt),r);break}en(e,Se,lt);break;case 5:en(e,Se,lt);break;default:throw Error(E(329))}}}return ke(e,q()),e.callbackNode===n?Kp.bind(null,e):null}function sa(e,t){var n=Br;return e.current.memoizedState.isDehydrated&&(cn(e,t).flags|=256),e=Os(e,t),e!==2&&(t=Se,Se=n,t!==null&&oa(t)),e}function oa(e){Se===null?Se=e:Se.push.apply(Se,e)}function h0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Ze(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~pu,t&=~oo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function df(e){if(O&6)throw Error(E(327));Gn();var t=vs(e,0);if(!(t&1))return ke(e,q()),null;var n=Os(e,t);if(e.tag!==0&&n===2){var r=Ll(e);r!==0&&(t=r,n=sa(e,r))}if(n===1)throw n=ai,cn(e,0),Et(e,t),ke(e,q()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,en(e,Se,lt),ke(e,q()),null}function gu(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(nr=q()+500,no&&Kt())}}function yn(e){jt!==null&&jt.tag===0&&!(O&6)&&Gn();var t=O;O|=1;var n=Be.transition,r=I;try{if(Be.transition=null,I=1,e)return e()}finally{I=r,Be.transition=n,O=t,!(O&6)&&Kt()}}function yu(){Ee=On.current,B(On)}function cn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wv(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(Za(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Cs();break;case 3:er(),B(Ce),B(ge),ou();break;case 5:su(r);break;case 4:er();break;case 13:B(W);break;case 19:B(W);break;case 10:tu(r.type._context);break;case 22:case 23:yu()}n=n.return}if(oe=e,ee=e=Ft(e.current,null),ue=Ee=t,re=0,ai=null,pu=oo=gn=0,Se=Br=null,on!==null){for(t=0;t<on.length;t++)if(n=on[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}on=null}return e}function Gp(e,t){do{var n=ee;try{if(eu(),rs.current=Ds,Ls){for(var r=b.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Ls=!1}if(mn=0,se=ne=b=null,Fr=!1,si=0,hu.current=null,n===null||n.return===null){re=1,ai=t,ee=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=qc(o);if(g!==null){g.flags&=-257,Jc(g,o,l,s,t),g.mode&1&&Zc(s,u,t),t=g,a=u;var y=t.updateQueue;if(y===null){var x=new Set;x.add(a),t.updateQueue=x}else y.add(a);break e}else{if(!(t&1)){Zc(s,u,t),vu();break e}a=Error(E(426))}}else if($&&l.mode&1){var T=qc(o);if(T!==null){!(T.flags&65536)&&(T.flags|=256),Jc(T,o,l,s,t),qa(tr(a,l));break e}}s=a=tr(a,l),re!==4&&(re=2),Br===null?Br=[s]:Br.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=Mp(s,a,t);bc(s,p);break e;case 1:l=a;var h=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Ot===null||!Ot.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=Lp(s,l,t);bc(s,w);break e}}s=s.return}while(s!==null)}Xp(n)}catch(C){t=C,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function Qp(){var e=Ns.current;return Ns.current=Ds,e===null?Ds:e}function vu(){(re===0||re===3||re===2)&&(re=4),oe===null||!(gn&268435455)&&!(oo&268435455)||Et(oe,ue)}function Os(e,t){var n=O;O|=2;var r=Qp();(oe!==e||ue!==t)&&(lt=null,cn(e,t));do try{p0();break}catch(i){Gp(e,i)}while(!0);if(eu(),O=n,Ns.current=r,ee!==null)throw Error(E(261));return oe=null,ue=0,re}function p0(){for(;ee!==null;)Yp(ee)}function m0(){for(;ee!==null&&!By();)Yp(ee)}function Yp(e){var t=qp(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?Xp(e):ee=t,hu.current=null}function Xp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=a0(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,ee=null;return}}else if(n=l0(n,t,Ee),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);re===0&&(re=5)}function en(e,t,n){var r=I,i=Be.transition;try{Be.transition=null,I=1,g0(e,t,n,r)}finally{Be.transition=i,I=r}return null}function g0(e,t,n,r){do Gn();while(jt!==null);if(O&6)throw Error(E(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Xy(e,s),e===oe&&(ee=oe=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Hi||(Hi=!0,Jp(ys,function(){return Gn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Be.transition,Be.transition=null;var o=I;I=1;var l=O;O|=4,hu.current=null,c0(e,n),Hp(n,e),Ov(Il),xs=!!Ol,Il=Ol=null,e.current=n,f0(n),Uy(),O=l,I=o,Be.transition=s}else e.current=n;if(Hi&&(Hi=!1,jt=e,Vs=i),s=e.pendingLanes,s===0&&(Ot=null),Hy(n.stateNode),ke(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(_s)throw _s=!1,e=ra,ra=null,e;return Vs&1&&e.tag!==0&&Gn(),s=e.pendingLanes,s&1?e===ia?Ur++:(Ur=0,ia=e):Ur=0,Kt(),null}function Gn(){if(jt!==null){var e=Mh(Vs),t=Be.transition,n=I;try{if(Be.transition=null,I=16>e?16:e,jt===null)var r=!1;else{if(e=jt,jt=null,Vs=0,O&6)throw Error(E(331));var i=O;for(O|=4,j=e.current;j!==null;){var s=j,o=s.child;if(j.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(j=u;j!==null;){var c=j;switch(c.tag){case 0:case 11:case 15:zr(8,c,s)}var f=c.child;if(f!==null)f.return=c,j=f;else for(;j!==null;){c=j;var d=c.sibling,g=c.return;if(Up(c),c===u){j=null;break}if(d!==null){d.return=g,j=d;break}j=g}}}var y=s.alternate;if(y!==null){var x=y.child;if(x!==null){y.child=null;do{var T=x.sibling;x.sibling=null,x=T}while(x!==null)}}j=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,j=o;else e:for(;j!==null;){if(s=j,s.flags&2048)switch(s.tag){case 0:case 11:case 15:zr(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,j=p;break e}j=s.return}}var h=e.current;for(j=h;j!==null;){o=j;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,j=m;else e:for(o=h;j!==null;){if(l=j,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:so(9,l)}}catch(C){Q(l,l.return,C)}if(l===o){j=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,j=w;break e}j=l.return}}if(O=i,Kt(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(Zs,e)}catch{}r=!0}return r}finally{I=n,Be.transition=t}}return!1}function hf(e,t,n){t=tr(n,t),t=Mp(e,t,1),e=Vt(e,t,1),t=ve(),e!==null&&(yi(e,1,t),ke(e,t))}function Q(e,t,n){if(e.tag===3)hf(e,e,n);else for(;t!==null;){if(t.tag===3){hf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ot===null||!Ot.has(r))){e=tr(n,e),e=Lp(t,e,1),t=Vt(t,e,1),e=ve(),t!==null&&(yi(t,1,e),ke(t,e));break}}t=t.return}}function y0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ve(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(ue&n)===n&&(re===4||re===3&&(ue&130023424)===ue&&500>q()-mu?cn(e,0):pu|=n),ke(e,t)}function Zp(e,t){t===0&&(e.mode&1?(t=_i,_i<<=1,!(_i&130023424)&&(_i=4194304)):t=1);var n=ve();e=gt(e,t),e!==null&&(yi(e,t,n),ke(e,n))}function v0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Zp(e,n)}function x0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),Zp(e,n)}var qp;qp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)Te=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Te=!1,o0(e,t,n);Te=!!(e.flags&131072)}else Te=!1,$&&t.flags&1048576&&np(t,Es,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ss(e,t),e=t.pendingProps;var i=Zn(t,ge.current);Kn(t,n),i=au(null,t,r,e,i,n);var s=uu();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(s=!0,Ps(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,ru(t),i.updater=io,t.stateNode=i,i._reactInternals=t,Kl(t,r,e,n),t=Yl(null,t,r,!0,s,n)):(t.tag=0,$&&s&&Xa(t),ye(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ss(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=S0(r),e=be(r,e),i){case 0:t=Ql(null,t,r,e,n);break e;case 1:t=nf(null,t,r,e,n);break e;case 11:t=ef(null,t,r,e,n);break e;case 14:t=tf(null,t,r,be(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),Ql(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),nf(e,t,r,i,n);case 3:e:{if(Vp(t),e===null)throw Error(E(387));r=t.pendingProps,s=t.memoizedState,i=s.element,ap(e,t),js(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=tr(Error(E(423)),t),t=rf(e,t,r,n,i);break e}else if(r!==i){i=tr(Error(E(424)),t),t=rf(e,t,r,n,i);break e}else for(Ae=_t(t.stateNode.containerInfo.firstChild),Re=t,$=!0,Ge=null,n=op(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(qn(),r===i){t=yt(e,t,n);break e}ye(e,t,r,n)}t=t.child}return t;case 5:return up(t),e===null&&Wl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Fl(r,i)?o=null:s!==null&&Fl(r,s)&&(t.flags|=32),_p(e,t),ye(e,t,o,n),t.child;case 6:return e===null&&Wl(t),null;case 13:return Op(e,t,n);case 4:return iu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Jn(t,null,r,n):ye(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),ef(e,t,r,i,n);case 7:return ye(e,t,t.pendingProps,n),t.child;case 8:return ye(e,t,t.pendingProps.children,n),t.child;case 12:return ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,F(As,r._currentValue),r._currentValue=o,s!==null)if(Ze(s.value,o)){if(s.children===i.children&&!Ce.current){t=yt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=ft(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Hl(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(E(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Hl(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}ye(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Kn(t,n),i=Ue(i),r=r(i),t.flags|=1,ye(e,t,r,n),t.child;case 14:return r=t.type,i=be(r,t.pendingProps),i=be(r.type,i),tf(e,t,r,i,n);case 15:return Dp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),ss(e,t),t.tag=1,Pe(r)?(e=!0,Ps(t)):e=!1,Kn(t,n),jp(t,r,i),Kl(t,r,i,n),Yl(null,t,r,!0,e,n);case 19:return Ip(e,t,n);case 22:return Np(e,t,n)}throw Error(E(156,t.tag))};function Jp(e,t){return Eh(e,t)}function w0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new w0(e,t,n,r)}function xu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function S0(e){if(typeof e=="function")return xu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Fa)return 11;if(e===za)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function as(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")xu(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case En:return fn(n.children,i,s,t);case Ia:o=8,i|=8;break;case gl:return e=ze(12,n,t,i|2),e.elementType=gl,e.lanes=s,e;case yl:return e=ze(13,n,t,i),e.elementType=yl,e.lanes=s,e;case vl:return e=ze(19,n,t,i),e.elementType=vl,e.lanes=s,e;case uh:return lo(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case lh:o=10;break e;case ah:o=9;break e;case Fa:o=11;break e;case za:o=14;break e;case Ct:o=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=ze(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function fn(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function lo(e,t,n,r){return e=ze(22,e,r,t),e.elementType=uh,e.lanes=n,e.stateNode={isHidden:!1},e}function Go(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function Qo(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function T0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ro(0),this.expirationTimes=Ro(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ro(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function wu(e,t,n,r,i,s,o,l,a){return e=new T0(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=ze(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ru(s),e}function C0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:kn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function em(e){if(!e)return Ut;e=e._reactInternals;e:{if(xn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(Pe(n))return ep(e,n,t)}return t}function tm(e,t,n,r,i,s,o,l,a){return e=wu(n,r,!0,e,i,s,o,l,a),e.context=em(null),n=e.current,r=ve(),i=It(n),s=ft(r,i),s.callback=t??null,Vt(n,s,i),e.current.lanes=i,yi(e,i,r),ke(e,r),e}function ao(e,t,n,r){var i=t.current,s=ve(),o=It(i);return n=em(n),t.context===null?t.context=n:t.pendingContext=n,t=ft(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Vt(i,t,o),e!==null&&(Xe(e,i,o,s),ns(e,i,o)),o}function Is(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function pf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Su(e,t){pf(e,t),(e=e.alternate)&&pf(e,t)}function P0(){return null}var nm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Tu(e){this._internalRoot=e}uo.prototype.render=Tu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));ao(e,t,null,null)};uo.prototype.unmount=Tu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yn(function(){ao(null,e,null,null)}),t[mt]=null}};function uo(e){this._internalRoot=e}uo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<kt.length&&t!==0&&t<kt[n].priority;n++);kt.splice(n,0,e),n===0&&Vh(e)}};function Cu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function co(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function mf(){}function k0(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=Is(o);s.call(u)}}var o=tm(t,r,e,0,null,!1,!1,"",mf);return e._reactRootContainer=o,e[mt]=o.current,ei(e.nodeType===8?e.parentNode:e),yn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=Is(a);l.call(u)}}var a=wu(e,0,!1,null,null,!1,!1,"",mf);return e._reactRootContainer=a,e[mt]=a.current,ei(e.nodeType===8?e.parentNode:e),yn(function(){ao(t,a,n,r)}),a}function fo(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=Is(o);l.call(a)}}ao(t,o,e,i)}else o=k0(n,t,e,i,r);return Is(o)}Lh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rr(t.pendingLanes);n!==0&&($a(t,n|1),ke(t,q()),!(O&6)&&(nr=q()+500,Kt()))}break;case 13:yn(function(){var r=gt(e,1);if(r!==null){var i=ve();Xe(r,e,1,i)}}),Su(e,1)}};Wa=function(e){if(e.tag===13){var t=gt(e,134217728);if(t!==null){var n=ve();Xe(t,e,134217728,n)}Su(e,134217728)}};Dh=function(e){if(e.tag===13){var t=It(e),n=gt(e,t);if(n!==null){var r=ve();Xe(n,e,t,r)}Su(e,t)}};Nh=function(){return I};_h=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Rl=function(e,t,n){switch(t){case"input":if(Sl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=to(r);if(!i)throw Error(E(90));fh(r),Sl(r,i)}}}break;case"textarea":hh(e,n);break;case"select":t=n.value,t!=null&&$n(e,!!n.multiple,t,!1)}};wh=gu;Sh=yn;var E0={usingClientEntryPoint:!1,Events:[xi,Mn,to,vh,xh,gu]},Tr={findFiberByHostInstance:sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},A0={bundleType:Tr.bundleType,version:Tr.version,rendererPackageName:Tr.rendererPackageName,rendererConfig:Tr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ph(e),e===null?null:e.stateNode},findFiberByHostInstance:Tr.findFiberByHostInstance||P0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bi.isDisabled&&bi.supportsFiber)try{Zs=bi.inject(A0),nt=bi}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=E0;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Cu(t))throw Error(E(200));return C0(e,t,null,n)};De.createRoot=function(e,t){if(!Cu(e))throw Error(E(299));var n=!1,r="",i=nm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=wu(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,ei(e.nodeType===8?e.parentNode:e),new Tu(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=Ph(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return yn(e)};De.hydrate=function(e,t,n){if(!co(t))throw Error(E(200));return fo(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!Cu(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=nm;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=tm(t,null,e,1,n??null,i,!1,s,o),e[mt]=t.current,ei(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new uo(t)};De.render=function(e,t,n){if(!co(t))throw Error(E(200));return fo(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!co(e))throw Error(E(40));return e._reactRootContainer?(yn(function(){fo(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};De.unstable_batchedUpdates=gu;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!co(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return fo(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function rm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(rm)}catch(e){console.error(e)}}rm(),rh.exports=De;var R0=rh.exports,gf=R0;pl.createRoot=gf.createRoot,pl.hydrateRoot=gf.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}var Mt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Mt||(Mt={}));const yf="popstate";function j0(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:l}=r.location;return la("",{pathname:s,search:o,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Fs(i)}return L0(t,n,null,e)}function Y(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function im(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function M0(){return Math.random().toString(36).substr(2,8)}function vf(e,t){return{usr:e.state,key:e.key,idx:t}}function la(e,t,n,r){return n===void 0&&(n=null),ui({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?cr(t):t,{state:n,key:t&&t.key||r||M0()})}function Fs(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function cr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function L0(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,l=Mt.Pop,a=null,u=c();u==null&&(u=0,o.replaceState(ui({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function f(){l=Mt.Pop;let T=c(),p=T==null?null:T-u;u=T,a&&a({action:l,location:x.location,delta:p})}function d(T,p){l=Mt.Push;let h=la(x.location,T,p);u=c()+1;let m=vf(h,u),w=x.createHref(h);try{o.pushState(m,"",w)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;i.location.assign(w)}s&&a&&a({action:l,location:x.location,delta:1})}function g(T,p){l=Mt.Replace;let h=la(x.location,T,p);u=c();let m=vf(h,u),w=x.createHref(h);o.replaceState(m,"",w),s&&a&&a({action:l,location:x.location,delta:0})}function y(T){let p=i.location.origin!=="null"?i.location.origin:i.location.href,h=typeof T=="string"?T:Fs(T);return h=h.replace(/ $/,"%20"),Y(p,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,p)}let x={get action(){return l},get location(){return e(i,o)},listen(T){if(a)throw new Error("A history only accepts one active listener");return i.addEventListener(yf,f),a=T,()=>{i.removeEventListener(yf,f),a=null}},createHref(T){return t(i,T)},createURL:y,encodeLocation(T){let p=y(T);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:d,replace:g,go(T){return o.go(T)}};return x}var xf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(xf||(xf={}));function D0(e,t,n){return n===void 0&&(n="/"),N0(e,t,n)}function N0(e,t,n,r){let i=typeof t=="string"?cr(t):t,s=rr(i.pathname||"/",n);if(s==null)return null;let o=sm(e);_0(o);let l=null;for(let a=0;l==null&&a<o.length;++a){let u=b0(s);l=W0(o[a],u)}return l}function sm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,l)=>{let a={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};a.relativePath.startsWith("/")&&(Y(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=zt([r,a.relativePath]),c=n.concat(a);s.children&&s.children.length>0&&(Y(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),sm(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:U0(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))i(s,o);else for(let a of om(s.path))i(s,o,a)}),t}function om(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=om(r.join("/")),l=[];return l.push(...o.map(a=>a===""?s:[s,a].join("/"))),i&&l.push(...o),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function _0(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:$0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const V0=/^:[\w-]+$/,O0=3,I0=2,F0=1,z0=10,B0=-2,wf=e=>e==="*";function U0(e,t){let n=e.split("/"),r=n.length;return n.some(wf)&&(r+=B0),t&&(r+=I0),n.filter(i=>!wf(i)).reduce((i,s)=>i+(V0.test(s)?O0:s===""?F0:z0),r)}function $0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function W0(e,t,n){let{routesMeta:r}=e,i={},s="/",o=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",f=aa({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},c),d=a.route;if(!f)return null;Object.assign(i,f.params),o.push({params:i,pathname:zt([s,f.pathname]),pathnameBase:Y0(zt([s,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(s=zt([s,f.pathnameBase]))}return o}function aa(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=H0(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),l=i.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:d,isOptional:g}=c;if(d==="*"){let x=l[f]||"";o=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const y=l[f];return g&&!y?u[d]=void 0:u[d]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function H0(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),im(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function b0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return im(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function rr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function K0(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?cr(e):e;return{pathname:n?n.startsWith("/")?n:G0(n,t):t,search:X0(r),hash:Z0(i)}}function G0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Yo(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Q0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function lm(e,t){let n=Q0(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function am(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=cr(e):(i=ui({},e),Y(!i.pathname||!i.pathname.includes("?"),Yo("?","pathname","search",i)),Y(!i.pathname||!i.pathname.includes("#"),Yo("#","pathname","hash",i)),Y(!i.search||!i.search.includes("#"),Yo("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,l;if(o==null)l=n;else{let f=t.length-1;if(!r&&o.startsWith("..")){let d=o.split("/");for(;d[0]==="..";)d.shift(),f-=1;i.pathname=d.join("/")}l=f>=0?t[f]:"/"}let a=K0(i,l),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||c)&&(a.pathname+="/"),a}const zt=e=>e.join("/").replace(/\/\/+/g,"/"),Y0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),X0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Z0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function q0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const um=["post","put","patch","delete"];new Set(um);const J0=["get",...um];new Set(J0);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ci(){return ci=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ci.apply(this,arguments)}const ho=S.createContext(null),cm=S.createContext(null),Gt=S.createContext(null),po=S.createContext(null),wn=S.createContext({outlet:null,matches:[],isDataRoute:!1}),fm=S.createContext(null);function ex(e,t){let{relative:n}=t===void 0?{}:t;Si()||Y(!1);let{basename:r,navigator:i}=S.useContext(Gt),{hash:s,pathname:o,search:l}=mo(e,{relative:n}),a=o;return r!=="/"&&(a=o==="/"?r:zt([r,o])),i.createHref({pathname:a,search:l,hash:s})}function Si(){return S.useContext(po)!=null}function fr(){return Si()||Y(!1),S.useContext(po).location}function dm(e){S.useContext(Gt).static||S.useLayoutEffect(e)}function tx(){let{isDataRoute:e}=S.useContext(wn);return e?px():nx()}function nx(){Si()||Y(!1);let e=S.useContext(ho),{basename:t,future:n,navigator:r}=S.useContext(Gt),{matches:i}=S.useContext(wn),{pathname:s}=fr(),o=JSON.stringify(lm(i,n.v7_relativeSplatPath)),l=S.useRef(!1);return dm(()=>{l.current=!0}),S.useCallback(function(u,c){if(c===void 0&&(c={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let f=am(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:zt([t,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[t,r,o,s,e])}function mo(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=S.useContext(Gt),{matches:i}=S.useContext(wn),{pathname:s}=fr(),o=JSON.stringify(lm(i,r.v7_relativeSplatPath));return S.useMemo(()=>am(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function rx(e,t){return ix(e,t)}function ix(e,t,n,r){Si()||Y(!1);let{navigator:i}=S.useContext(Gt),{matches:s}=S.useContext(wn),o=s[s.length-1],l=o?o.params:{};o&&o.pathname;let a=o?o.pathnameBase:"/";o&&o.route;let u=fr(),c;if(t){var f;let T=typeof t=="string"?cr(t):t;a==="/"||(f=T.pathname)!=null&&f.startsWith(a)||Y(!1),c=T}else c=u;let d=c.pathname||"/",g=d;if(a!=="/"){let T=a.replace(/^\//,"").split("/");g="/"+d.replace(/^\//,"").split("/").slice(T.length).join("/")}let y=D0(e,{pathname:g}),x=ux(y&&y.map(T=>Object.assign({},T,{params:Object.assign({},l,T.params),pathname:zt([a,i.encodeLocation?i.encodeLocation(T.pathname).pathname:T.pathname]),pathnameBase:T.pathnameBase==="/"?a:zt([a,i.encodeLocation?i.encodeLocation(T.pathnameBase).pathname:T.pathnameBase])})),s,n,r);return t&&x?S.createElement(po.Provider,{value:{location:ci({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Mt.Pop}},x):x}function sx(){let e=hx(),t=q0(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},t),n?S.createElement("pre",{style:i},n):null,null)}const ox=S.createElement(sx,null);class lx extends S.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?S.createElement(wn.Provider,{value:this.props.routeContext},S.createElement(fm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ax(e){let{routeContext:t,match:n,children:r}=e,i=S.useContext(ho);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),S.createElement(wn.Provider,{value:t},r)}function ux(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,l=(i=n)==null?void 0:i.errors;if(l!=null){let c=o.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);c>=0||Y(!1),o=o.slice(0,Math.min(o.length,c+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let f=o[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:d,errors:g}=n,y=f.route.loader&&d[f.route.id]===void 0&&(!g||g[f.route.id]===void 0);if(f.route.lazy||y){a=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,f,d)=>{let g,y=!1,x=null,T=null;n&&(g=l&&f.route.id?l[f.route.id]:void 0,x=f.route.errorElement||ox,a&&(u<0&&d===0?(mx("route-fallback"),y=!0,T=null):u===d&&(y=!0,T=f.route.hydrateFallbackElement||null)));let p=t.concat(o.slice(0,d+1)),h=()=>{let m;return g?m=x:y?m=T:f.route.Component?m=S.createElement(f.route.Component,null):f.route.element?m=f.route.element:m=c,S.createElement(ax,{match:f,routeContext:{outlet:c,matches:p,isDataRoute:n!=null},children:m})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?S.createElement(lx,{location:n.location,revalidation:n.revalidation,component:x,error:g,children:h(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):h()},null)}var hm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(hm||{}),pm=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(pm||{});function cx(e){let t=S.useContext(ho);return t||Y(!1),t}function fx(e){let t=S.useContext(cm);return t||Y(!1),t}function dx(e){let t=S.useContext(wn);return t||Y(!1),t}function mm(e){let t=dx(),n=t.matches[t.matches.length-1];return n.route.id||Y(!1),n.route.id}function hx(){var e;let t=S.useContext(fm),n=fx(),r=mm();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function px(){let{router:e}=cx(hm.UseNavigateStable),t=mm(pm.UseNavigateStable),n=S.useRef(!1);return dm(()=>{n.current=!0}),S.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,ci({fromRouteId:t},s)))},[e,t])}const Sf={};function mx(e,t,n){Sf[e]||(Sf[e]=!0)}function gx(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function tn(e){Y(!1)}function yx(e){let{basename:t="/",children:n=null,location:r,navigationType:i=Mt.Pop,navigator:s,static:o=!1,future:l}=e;Si()&&Y(!1);let a=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:a,navigator:s,static:o,future:ci({v7_relativeSplatPath:!1},l)}),[a,l,s,o]);typeof r=="string"&&(r=cr(r));let{pathname:c="/",search:f="",hash:d="",state:g=null,key:y="default"}=r,x=S.useMemo(()=>{let T=rr(c,a);return T==null?null:{location:{pathname:T,search:f,hash:d,state:g,key:y},navigationType:i}},[a,c,f,d,g,y,i]);return x==null?null:S.createElement(Gt.Provider,{value:u},S.createElement(po.Provider,{children:n,value:x}))}function vx(e){let{children:t,location:n}=e;return rx(ua(t),n)}new Promise(()=>{});function ua(e,t){t===void 0&&(t=[]);let n=[];return S.Children.forEach(e,(r,i)=>{if(!S.isValidElement(r))return;let s=[...t,i];if(r.type===S.Fragment){n.push.apply(n,ua(r.props.children,s));return}r.type!==tn&&Y(!1),!r.props.index||!r.props.children||Y(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=ua(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function zs(){return zs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zs.apply(this,arguments)}function gm(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function xx(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function wx(e,t){return e.button===0&&(!t||t==="_self")&&!xx(e)}const Sx=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Tx=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],Cx="6";try{window.__reactRouterVersion=Cx}catch{}const Px=S.createContext({isTransitioning:!1}),kx="startTransition",Tf=yy[kx];function Ex(e){let{basename:t,children:n,future:r,window:i}=e,s=S.useRef();s.current==null&&(s.current=j0({window:i,v5Compat:!0}));let o=s.current,[l,a]=S.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=S.useCallback(f=>{u&&Tf?Tf(()=>a(f)):a(f)},[a,u]);return S.useLayoutEffect(()=>o.listen(c),[o,c]),S.useEffect(()=>gx(r),[r]),S.createElement(yx,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:o,future:r})}const Ax=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Rx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Bs=S.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:l,target:a,to:u,preventScrollReset:c,viewTransition:f}=t,d=gm(t,Sx),{basename:g}=S.useContext(Gt),y,x=!1;if(typeof u=="string"&&Rx.test(u)&&(y=u,Ax))try{let m=new URL(window.location.href),w=u.startsWith("//")?new URL(m.protocol+u):new URL(u),C=rr(w.pathname,g);w.origin===m.origin&&C!=null?u=C+w.search+w.hash:x=!0}catch{}let T=ex(u,{relative:i}),p=Lx(u,{replace:o,state:l,target:a,preventScrollReset:c,relative:i,viewTransition:f});function h(m){r&&r(m),m.defaultPrevented||p(m)}return S.createElement("a",zs({},d,{href:y||T,onClick:x||s?r:h,ref:n,target:a}))}),jx=S.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:o=!1,style:l,to:a,viewTransition:u,children:c}=t,f=gm(t,Tx),d=mo(a,{relative:f.relative}),g=fr(),y=S.useContext(cm),{navigator:x,basename:T}=S.useContext(Gt),p=y!=null&&Dx(d)&&u===!0,h=x.encodeLocation?x.encodeLocation(d).pathname:d.pathname,m=g.pathname,w=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;i||(m=m.toLowerCase(),w=w?w.toLowerCase():null,h=h.toLowerCase()),w&&T&&(w=rr(w,T)||w);const C=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let k=m===h||!o&&m.startsWith(h)&&m.charAt(C)==="/",A=w!=null&&(w===h||!o&&w.startsWith(h)&&w.charAt(h.length)==="/"),P={isActive:k,isPending:A,isTransitioning:p},_=k?r:void 0,M;typeof s=="function"?M=s(P):M=[s,k?"active":null,A?"pending":null,p?"transitioning":null].filter(Boolean).join(" ");let J=typeof l=="function"?l(P):l;return S.createElement(Bs,zs({},f,{"aria-current":_,className:M,ref:n,style:J,to:a,viewTransition:u}),typeof c=="function"?c(P):c)});var ca;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(ca||(ca={}));var Cf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Cf||(Cf={}));function Mx(e){let t=S.useContext(ho);return t||Y(!1),t}function Lx(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:l}=t===void 0?{}:t,a=tx(),u=fr(),c=mo(e,{relative:o});return S.useCallback(f=>{if(wx(f,n)){f.preventDefault();let d=r!==void 0?r:Fs(u)===Fs(c);a(e,{replace:d,state:i,preventScrollReset:s,relative:o,viewTransition:l})}},[u,a,c,r,i,n,e,s,o,l])}function Dx(e,t){t===void 0&&(t={});let n=S.useContext(Px);n==null&&Y(!1);let{basename:r}=Mx(ca.useViewTransitionState),i=mo(e,{relative:t.relative});if(!n.isTransitioning)return!1;let s=rr(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=rr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return aa(i.pathname,o)!=null||aa(i.pathname,s)!=null}var Nx=typeof Element<"u",_x=typeof Map=="function",Vx=typeof Set=="function",Ox=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function us(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var n,r,i;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(!us(e[r],t[r]))return!1;return!0}var s;if(_x&&e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(s=e.entries();!(r=s.next()).done;)if(!t.has(r.value[0]))return!1;for(s=e.entries();!(r=s.next()).done;)if(!us(r.value[1],t.get(r.value[0])))return!1;return!0}if(Vx&&e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(s=e.entries();!(r=s.next()).done;)if(!t.has(r.value[0]))return!1;return!0}if(Ox&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf&&typeof e.valueOf=="function"&&typeof t.valueOf=="function")return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString&&typeof e.toString=="function"&&typeof t.toString=="function")return e.toString()===t.toString();if(i=Object.keys(e),n=i.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!Object.prototype.hasOwnProperty.call(t,i[r]))return!1;if(Nx&&e instanceof Element)return!1;for(r=n;r--!==0;)if(!((i[r]==="_owner"||i[r]==="__v"||i[r]==="__o")&&e.$$typeof)&&!us(e[i[r]],t[i[r]]))return!1;return!0}return e!==e&&t!==t}var Ix=function(t,n){try{return us(t,n)}catch(r){if((r.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw r}};const Fx=Ys(Ix);var zx=function(e,t,n,r,i,s,o,l){if(!e){var a;if(t===void 0)a=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,r,i,s,o,l],c=0;a=new Error(t.replace(/%s/g,function(){return u[c++]})),a.name="Invariant Violation"}throw a.framesToPop=1,a}},Bx=zx;const Pf=Ys(Bx);var Ux=function(t,n,r,i){var s=r?r.call(i,t,n):void 0;if(s!==void 0)return!!s;if(t===n)return!0;if(typeof t!="object"||!t||typeof n!="object"||!n)return!1;var o=Object.keys(t),l=Object.keys(n);if(o.length!==l.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(n),u=0;u<o.length;u++){var c=o[u];if(!a(c))return!1;var f=t[c],d=n[c];if(s=r?r.call(i,f,d,c):void 0,s===!1||s===void 0&&f!==d)return!1}return!0};const $x=Ys(Ux);var ym=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(ym||{}),Xo={link:{rel:["amphtml","canonical","alternate"]},script:{type:["application/ld+json"]},meta:{charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]}},kf=Object.values(ym),Pu={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},Wx=Object.entries(Pu).reduce((e,[t,n])=>(e[n]=t,e),{}),Qe="data-rh",Qn={DEFAULT_TITLE:"defaultTitle",DEFER:"defer",ENCODE_SPECIAL_CHARACTERS:"encodeSpecialCharacters",ON_CHANGE_CLIENT_STATE:"onChangeClientState",TITLE_TEMPLATE:"titleTemplate",PRIORITIZE_SEO_TAGS:"prioritizeSeoTags"},Yn=(e,t)=>{for(let n=e.length-1;n>=0;n-=1){const r=e[n];if(Object.prototype.hasOwnProperty.call(r,t))return r[t]}return null},Hx=e=>{let t=Yn(e,"title");const n=Yn(e,Qn.TITLE_TEMPLATE);if(Array.isArray(t)&&(t=t.join("")),n&&t)return n.replace(/%s/g,()=>t);const r=Yn(e,Qn.DEFAULT_TITLE);return t||r||void 0},bx=e=>Yn(e,Qn.ON_CHANGE_CLIENT_STATE)||(()=>{}),Zo=(e,t)=>t.filter(n=>typeof n[e]<"u").map(n=>n[e]).reduce((n,r)=>({...n,...r}),{}),Kx=(e,t)=>t.filter(n=>typeof n.base<"u").map(n=>n.base).reverse().reduce((n,r)=>{if(!n.length){const i=Object.keys(r);for(let s=0;s<i.length;s+=1){const l=i[s].toLowerCase();if(e.indexOf(l)!==-1&&r[l])return n.concat(r)}}return n},[]),Gx=e=>console&&typeof console.warn=="function"&&console.warn(e),Cr=(e,t,n)=>{const r={};return n.filter(i=>Array.isArray(i[e])?!0:(typeof i[e]<"u"&&Gx(`Helmet: ${e} should be of type "Array". Instead found type "${typeof i[e]}"`),!1)).map(i=>i[e]).reverse().reduce((i,s)=>{const o={};s.filter(a=>{let u;const c=Object.keys(a);for(let d=0;d<c.length;d+=1){const g=c[d],y=g.toLowerCase();t.indexOf(y)!==-1&&!(u==="rel"&&a[u].toLowerCase()==="canonical")&&!(y==="rel"&&a[y].toLowerCase()==="stylesheet")&&(u=y),t.indexOf(g)!==-1&&(g==="innerHTML"||g==="cssText"||g==="itemprop")&&(u=g)}if(!u||!a[u])return!1;const f=a[u].toLowerCase();return r[u]||(r[u]={}),o[u]||(o[u]={}),r[u][f]?!1:(o[u][f]=!0,!0)}).reverse().forEach(a=>i.push(a));const l=Object.keys(o);for(let a=0;a<l.length;a+=1){const u=l[a],c={...r[u],...o[u]};r[u]=c}return i},[]).reverse()},Qx=(e,t)=>{if(Array.isArray(e)&&e.length){for(let n=0;n<e.length;n+=1)if(e[n][t])return!0}return!1},Yx=e=>({baseTag:Kx(["href"],e),bodyAttributes:Zo("bodyAttributes",e),defer:Yn(e,Qn.DEFER),encode:Yn(e,Qn.ENCODE_SPECIAL_CHARACTERS),htmlAttributes:Zo("htmlAttributes",e),linkTags:Cr("link",["rel","href"],e),metaTags:Cr("meta",["name","charset","http-equiv","property","itemprop"],e),noscriptTags:Cr("noscript",["innerHTML"],e),onChangeClientState:bx(e),scriptTags:Cr("script",["src","innerHTML"],e),styleTags:Cr("style",["cssText"],e),title:Hx(e),titleAttributes:Zo("titleAttributes",e),prioritizeSeoTags:Qx(e,Qn.PRIORITIZE_SEO_TAGS)}),vm=e=>Array.isArray(e)?e.join(""):e,Xx=(e,t)=>{const n=Object.keys(e);for(let r=0;r<n.length;r+=1)if(t[n[r]]&&t[n[r]].includes(e[n[r]]))return!0;return!1},qo=(e,t)=>Array.isArray(e)?e.reduce((n,r)=>(Xx(r,t)?n.priority.push(r):n.default.push(r),n),{priority:[],default:[]}):{default:e,priority:[]},Ef=(e,t)=>({...e,[t]:void 0}),Zx=["noscript","script","style"],fa=(e,t=!0)=>t===!1?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),xm=e=>Object.keys(e).reduce((t,n)=>{const r=typeof e[n]<"u"?`${n}="${e[n]}"`:`${n}`;return t?`${t} ${r}`:r},""),qx=(e,t,n,r)=>{const i=xm(n),s=vm(t);return i?`<${e} ${Qe}="true" ${i}>${fa(s,r)}</${e}>`:`<${e} ${Qe}="true">${fa(s,r)}</${e}>`},Jx=(e,t,n=!0)=>t.reduce((r,i)=>{const s=i,o=Object.keys(s).filter(u=>!(u==="innerHTML"||u==="cssText")).reduce((u,c)=>{const f=typeof s[c]>"u"?c:`${c}="${fa(s[c],n)}"`;return u?`${u} ${f}`:f},""),l=s.innerHTML||s.cssText||"",a=Zx.indexOf(e)===-1;return`${r}<${e} ${Qe}="true" ${o}${a?"/>":`>${l}</${e}>`}`},""),wm=(e,t={})=>Object.keys(e).reduce((n,r)=>{const i=Pu[r];return n[i||r]=e[r],n},t),e1=(e,t,n)=>{const r={key:t,[Qe]:!0},i=wm(n,r);return[tt.createElement("title",i,t)]},cs=(e,t)=>t.map((n,r)=>{const i={key:r,[Qe]:!0};return Object.keys(n).forEach(s=>{const l=Pu[s]||s;if(l==="innerHTML"||l==="cssText"){const a=n.innerHTML||n.cssText;i.dangerouslySetInnerHTML={__html:a}}else i[l]=n[s]}),tt.createElement(e,i)}),Oe=(e,t,n=!0)=>{switch(e){case"title":return{toComponent:()=>e1(e,t.title,t.titleAttributes),toString:()=>qx(e,t.title,t.titleAttributes,n)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>wm(t),toString:()=>xm(t)};default:return{toComponent:()=>cs(e,t),toString:()=>Jx(e,t,n)}}},t1=({metaTags:e,linkTags:t,scriptTags:n,encode:r})=>{const i=qo(e,Xo.meta),s=qo(t,Xo.link),o=qo(n,Xo.script);return{priorityMethods:{toComponent:()=>[...cs("meta",i.priority),...cs("link",s.priority),...cs("script",o.priority)],toString:()=>`${Oe("meta",i.priority,r)} ${Oe("link",s.priority,r)} ${Oe("script",o.priority,r)}`},metaTags:i.default,linkTags:s.default,scriptTags:o.default}},n1=e=>{const{baseTag:t,bodyAttributes:n,encode:r=!0,htmlAttributes:i,noscriptTags:s,styleTags:o,title:l="",titleAttributes:a,prioritizeSeoTags:u}=e;let{linkTags:c,metaTags:f,scriptTags:d}=e,g={toComponent:()=>{},toString:()=>""};return u&&({priorityMethods:g,linkTags:c,metaTags:f,scriptTags:d}=t1(e)),{priority:g,base:Oe("base",t,r),bodyAttributes:Oe("bodyAttributes",n,r),htmlAttributes:Oe("htmlAttributes",i,r),link:Oe("link",c,r),meta:Oe("meta",f,r),noscript:Oe("noscript",s,r),script:Oe("script",d,r),style:Oe("style",o,r),title:Oe("title",{title:l,titleAttributes:a},r)}},da=n1,Ki=[],Sm=!!(typeof window<"u"&&window.document&&window.document.createElement),ha=class{constructor(e,t){ot(this,"instances",[]);ot(this,"canUseDOM",Sm);ot(this,"context");ot(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?Ki:this.instances,add:e=>{(this.canUseDOM?Ki:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?Ki:this.instances).indexOf(e);(this.canUseDOM?Ki:this.instances).splice(t,1)}}});this.context=e,this.canUseDOM=t||!1,t||(e.helmet=da({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},r1={},Tm=tt.createContext(r1),un,Cm=(un=class extends S.Component{constructor(n){super(n);ot(this,"helmetData");this.helmetData=new ha(this.props.context||{},un.canUseDOM)}render(){return tt.createElement(Tm.Provider,{value:this.helmetData.value},this.props.children)}},ot(un,"canUseDOM",Sm),un),Pn=(e,t)=>{const n=document.head||document.querySelector("head"),r=n.querySelectorAll(`${e}[${Qe}]`),i=[].slice.call(r),s=[];let o;return t&&t.length&&t.forEach(l=>{const a=document.createElement(e);for(const u in l)if(Object.prototype.hasOwnProperty.call(l,u))if(u==="innerHTML")a.innerHTML=l.innerHTML;else if(u==="cssText")a.styleSheet?a.styleSheet.cssText=l.cssText:a.appendChild(document.createTextNode(l.cssText));else{const c=u,f=typeof l[c]>"u"?"":l[c];a.setAttribute(u,f)}a.setAttribute(Qe,"true"),i.some((u,c)=>(o=c,a.isEqualNode(u)))?i.splice(o,1):s.push(a)}),i.forEach(l=>{var a;return(a=l.parentNode)==null?void 0:a.removeChild(l)}),s.forEach(l=>n.appendChild(l)),{oldTags:i,newTags:s}},pa=(e,t)=>{const n=document.getElementsByTagName(e)[0];if(!n)return;const r=n.getAttribute(Qe),i=r?r.split(","):[],s=[...i],o=Object.keys(t);for(const l of o){const a=t[l]||"";n.getAttribute(l)!==a&&n.setAttribute(l,a),i.indexOf(l)===-1&&i.push(l);const u=s.indexOf(l);u!==-1&&s.splice(u,1)}for(let l=s.length-1;l>=0;l-=1)n.removeAttribute(s[l]);i.length===s.length?n.removeAttribute(Qe):n.getAttribute(Qe)!==o.join(",")&&n.setAttribute(Qe,o.join(","))},i1=(e,t)=>{typeof e<"u"&&document.title!==e&&(document.title=vm(e)),pa("title",t)},Af=(e,t)=>{const{baseTag:n,bodyAttributes:r,htmlAttributes:i,linkTags:s,metaTags:o,noscriptTags:l,onChangeClientState:a,scriptTags:u,styleTags:c,title:f,titleAttributes:d}=e;pa("body",r),pa("html",i),i1(f,d);const g={baseTag:Pn("base",n),linkTags:Pn("link",s),metaTags:Pn("meta",o),noscriptTags:Pn("noscript",l),scriptTags:Pn("script",u),styleTags:Pn("style",c)},y={},x={};Object.keys(g).forEach(T=>{const{newTags:p,oldTags:h}=g[T];p.length&&(y[T]=p),h.length&&(x[T]=g[T].oldTags)}),t&&t(),a(e,y,x)},Pr=null,s1=e=>{Pr&&cancelAnimationFrame(Pr),e.defer?Pr=requestAnimationFrame(()=>{Af(e,()=>{Pr=null})}):(Af(e),Pr=null)},o1=s1,Rf=class extends S.Component{constructor(){super(...arguments);ot(this,"rendered",!1)}shouldComponentUpdate(t){return!$x(t,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:t}=this.props.context;t.remove(this),this.emitChange()}emitChange(){const{helmetInstances:t,setHelmet:n}=this.props.context;let r=null;const i=Yx(t.get().map(s=>{const o={...s.props};return delete o.context,o}));Cm.canUseDOM?o1(i):da&&(r=da(i)),n(r)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:t}=this.props.context;t.add(this),this.emitChange()}render(){return this.init(),null}},hl,Sn=(hl=class extends S.Component{shouldComponentUpdate(e){return!Fx(Ef(this.props,"helmetData"),Ef(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,n,r){return{...t,[e.type]:[...t[e.type]||[],{...n,...this.mapNestedChildrenToProps(e,r)}]}}mapObjectTypeChildren(e,t,n,r){switch(e.type){case"title":return{...t,[e.type]:r,titleAttributes:{...n}};case"body":return{...t,bodyAttributes:{...n}};case"html":return{...t,htmlAttributes:{...n}};default:return{...t,[e.type]:{...n}}}}mapArrayTypeChildrenToProps(e,t){let n={...t};return Object.keys(e).forEach(r=>{n={...n,[r]:e[r]}}),n}warnOnInvalidChildren(e,t){return Pf(kf.some(n=>e.type===n),typeof e.type=="function"?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${kf.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),Pf(!t||typeof t=="string"||Array.isArray(t)&&!t.some(n=>typeof n!="string"),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let n={};return tt.Children.forEach(e,r=>{if(!r||!r.props)return;const{children:i,...s}=r.props,o=Object.keys(s).reduce((a,u)=>(a[Wx[u]||u]=s[u],a),{});let{type:l}=r;switch(typeof l=="symbol"?l=l.toString():this.warnOnInvalidChildren(r,i),l){case"Symbol(react.fragment)":t=this.mapChildrenToProps(i,t);break;case"link":case"meta":case"noscript":case"script":case"style":n=this.flattenArrayTypeChildren(r,n,o,i);break;default:t=this.mapObjectTypeChildren(r,t,o,i);break}}),this.mapArrayTypeChildrenToProps(n,t)}render(){const{children:e,...t}=this.props;let n={...t},{helmetData:r}=t;if(e&&(n=this.mapChildrenToProps(e,n)),r&&!(r instanceof ha)){const i=r;r=new ha(i.context,!0),delete n.helmetData}return r?tt.createElement(Rf,{...n,context:r.value}):tt.createElement(Tm.Consumer,null,i=>tt.createElement(Rf,{...n,context:i}))}},ot(hl,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),hl);const ku=S.createContext({});function Eu(e){const t=S.useRef(null);return t.current===null&&(t.current=e()),t.current}const go=S.createContext(null),Au=S.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class l1 extends S.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function a1({children:e,isPresent:t}){const n=S.useId(),r=S.useRef(null),i=S.useRef({width:0,height:0,top:0,left:0}),{nonce:s}=S.useContext(Au);return S.useInsertionEffect(()=>{const{width:o,height:l,top:a,left:u}=i.current;if(t||!r.current||!o||!l)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return s&&(c.nonce=s),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${l}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),v.jsx(l1,{isPresent:t,childRef:r,sizeRef:i,children:S.cloneElement(e,{ref:r})})}const u1=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const l=Eu(c1),a=S.useId(),u=S.useCallback(f=>{l.set(f,!0);for(const d of l.values())if(!d)return;r&&r()},[l,r]),c=S.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:u,register:f=>(l.set(f,!1),()=>l.delete(f))}),s?[Math.random(),u]:[n,u]);return S.useMemo(()=>{l.forEach((f,d)=>l.set(d,!1))},[n]),S.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),o==="popLayout"&&(e=v.jsx(a1,{isPresent:n,children:e})),v.jsx(go.Provider,{value:c,children:e})};function c1(){return new Map}function Pm(e=!0){const t=S.useContext(go);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,s=S.useId();S.useEffect(()=>{e&&i(s)},[e]);const o=S.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,o]:[!0]}const Gi=e=>e.key||"";function jf(e){const t=[];return S.Children.forEach(e,n=>{S.isValidElement(n)&&t.push(n)}),t}const Ru=typeof window<"u",km=Ru?S.useLayoutEffect:S.useEffect,f1=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:s="sync",propagate:o=!1})=>{const[l,a]=Pm(o),u=S.useMemo(()=>jf(e),[e]),c=o&&!l?[]:u.map(Gi),f=S.useRef(!0),d=S.useRef(u),g=Eu(()=>new Map),[y,x]=S.useState(u),[T,p]=S.useState(u);km(()=>{f.current=!1,d.current=u;for(let w=0;w<T.length;w++){const C=Gi(T[w]);c.includes(C)?g.delete(C):g.get(C)!==!0&&g.set(C,!1)}},[T,c.length,c.join("-")]);const h=[];if(u!==y){let w=[...u];for(let C=0;C<T.length;C++){const k=T[C],A=Gi(k);c.includes(A)||(w.splice(C,0,k),h.push(k))}s==="wait"&&h.length&&(w=h),p(jf(w)),x(u);return}const{forceRender:m}=S.useContext(ku);return v.jsx(v.Fragment,{children:T.map(w=>{const C=Gi(w),k=o&&!l?!1:u===T||c.includes(C),A=()=>{if(g.has(C))g.set(C,!0);else return;let P=!0;g.forEach(_=>{_||(P=!1)}),P&&(m==null||m(),p(d.current),o&&(a==null||a()),r&&r())};return v.jsx(u1,{isPresent:k,initial:!f.current||n?void 0:!1,custom:k?void 0:t,presenceAffectsLayout:i,mode:s,onExitComplete:k?void 0:A,children:w},C)})})},je=e=>e;let Em=je;function ju(e){let t;return()=>(t===void 0&&(t=e()),t)}const ir=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},dt=e=>e*1e3,ht=e=>e/1e3,d1={useManualTiming:!1};function h1(e){let t=new Set,n=new Set,r=!1,i=!1;const s=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function l(u){s.has(u)&&(a.schedule(u),e()),u(o)}const a={schedule:(u,c=!1,f=!1)=>{const g=f&&r?t:n;return c&&s.add(u),g.has(u)||g.add(u),u},cancel:u=>{n.delete(u),s.delete(u)},process:u=>{if(o=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,i&&(i=!1,a.process(u))}};return a}const Qi=["read","resolveKeyframes","update","preRender","render","postRender"],p1=40;function Am(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=Qi.reduce((p,h)=>(p[h]=h1(s),p),{}),{read:l,resolveKeyframes:a,update:u,preRender:c,render:f,postRender:d}=o,g=()=>{const p=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(p-i.timestamp,p1),1),i.timestamp=p,i.isProcessing=!0,l.process(i),a.process(i),u.process(i),c.process(i),f.process(i),d.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(g))},y=()=>{n=!0,r=!0,i.isProcessing||e(g)};return{schedule:Qi.reduce((p,h)=>{const m=o[h];return p[h]=(w,C=!1,k=!1)=>(n||y(),m.schedule(w,C,k)),p},{}),cancel:p=>{for(let h=0;h<Qi.length;h++)o[Qi[h]].cancel(p)},state:i,steps:o}}const{schedule:U,cancel:$t,state:ae,steps:Jo}=Am(typeof requestAnimationFrame<"u"?requestAnimationFrame:je,!0),Rm=S.createContext({strict:!1}),Mf={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sr={};for(const e in Mf)sr[e]={isEnabled:t=>Mf[e].some(n=>!!t[n])};function m1(e){for(const t in e)sr[t]={...sr[t],...e[t]}}const g1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Us(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||g1.has(e)}let jm=e=>!Us(e);function y1(e){e&&(jm=t=>t.startsWith("on")?!Us(t):e(t))}try{y1(require("@emotion/is-prop-valid").default)}catch{}function v1(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(jm(i)||n===!0&&Us(i)||!t&&!Us(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function x1(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const yo=S.createContext({});function fi(e){return typeof e=="string"||Array.isArray(e)}function vo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Mu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Lu=["initial",...Mu];function xo(e){return vo(e.animate)||Lu.some(t=>fi(e[t]))}function Mm(e){return!!(xo(e)||e.variants)}function w1(e,t){if(xo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||fi(n)?n:void 0,animate:fi(r)?r:void 0}}return e.inherit!==!1?t:{}}function S1(e){const{initial:t,animate:n}=w1(e,S.useContext(yo));return S.useMemo(()=>({initial:t,animate:n}),[Lf(t),Lf(n)])}function Lf(e){return Array.isArray(e)?e.join(" "):e}const T1=Symbol.for("motionComponentSymbol");function In(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function C1(e,t,n){return S.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):In(n)&&(n.current=r))},[t])}const Du=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),P1="framerAppearId",Lm="data-"+Du(P1),{schedule:Nu}=Am(queueMicrotask,!1),Dm=S.createContext({});function k1(e,t,n,r,i){var s,o;const{visualElement:l}=S.useContext(yo),a=S.useContext(Rm),u=S.useContext(go),c=S.useContext(Au).reducedMotion,f=S.useRef(null);r=r||a.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const d=f.current,g=S.useContext(Dm);d&&!d.projection&&i&&(d.type==="html"||d.type==="svg")&&E1(f.current,n,i,g);const y=S.useRef(!1);S.useInsertionEffect(()=>{d&&y.current&&d.update(n,u)});const x=n[Lm],T=S.useRef(!!x&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return km(()=>{d&&(y.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),Nu.render(d.render),T.current&&d.animationState&&d.animationState.animateChanges())}),S.useEffect(()=>{d&&(!T.current&&d.animationState&&d.animationState.animateChanges(),T.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,x)}),T.current=!1))}),d}function E1(e,t,n,r){const{layoutId:i,layout:s,drag:o,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Nm(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||l&&In(l),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function Nm(e){if(e)return e.options.allowProjection!==!1?e.projection:Nm(e.parent)}function A1({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var s,o;e&&m1(e);function l(u,c){let f;const d={...S.useContext(Au),...u,layoutId:R1(u)},{isStatic:g}=d,y=S1(u),x=r(u,g);if(!g&&Ru){j1();const T=M1(d);f=T.MeasureLayout,y.visualElement=k1(i,x,d,t,T.ProjectionNode)}return v.jsxs(yo.Provider,{value:y,children:[f&&y.visualElement?v.jsx(f,{visualElement:y.visualElement,...d}):null,n(i,u,C1(x,y.visualElement,c),x,g,y.visualElement)]})}l.displayName=`motion.${typeof i=="string"?i:`create(${(o=(s=i.displayName)!==null&&s!==void 0?s:i.name)!==null&&o!==void 0?o:""})`}`;const a=S.forwardRef(l);return a[T1]=i,a}function R1({layoutId:e}){const t=S.useContext(ku).id;return t&&e!==void 0?t+"-"+e:e}function j1(e,t){S.useContext(Rm).strict}function M1(e){const{drag:t,layout:n}=sr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const L1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function _u(e){return typeof e!="string"||e.includes("-")?!1:!!(L1.indexOf(e)>-1||/[A-Z]/u.test(e))}function Df(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Vu(e,t,n,r){if(typeof t=="function"){const[i,s]=Df(r);t=t(n!==void 0?n:e.custom,i,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,s]=Df(r);t=t(n!==void 0?n:e.custom,i,s)}return t}const ma=e=>Array.isArray(e),D1=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),N1=e=>ma(e)?e[e.length-1]||0:e,me=e=>!!(e&&e.getVelocity);function fs(e){const t=me(e)?e.get():e;return D1(t)?t.toValue():t}function _1({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,s){const o={latestValues:V1(r,i,s,e),renderState:t()};return n&&(o.onMount=l=>n({props:r,current:l,...o}),o.onUpdate=l=>n(l)),o}const _m=e=>(t,n)=>{const r=S.useContext(yo),i=S.useContext(go),s=()=>_1(e,t,r,i);return n?s():Eu(s)};function V1(e,t,n,r){const i={},s=r(e,{});for(const d in s)i[d]=fs(s[d]);let{initial:o,animate:l}=e;const a=xo(e),u=Mm(e);t&&u&&!a&&e.inherit!==!1&&(o===void 0&&(o=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const f=c?l:o;if(f&&typeof f!="boolean"&&!vo(f)){const d=Array.isArray(f)?f:[f];for(let g=0;g<d.length;g++){const y=Vu(e,d[g]);if(y){const{transitionEnd:x,transition:T,...p}=y;for(const h in p){let m=p[h];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(i[h]=m)}for(const h in x)i[h]=x[h]}}}return i}const dr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Tn=new Set(dr),Vm=e=>t=>typeof t=="string"&&t.startsWith(e),Om=Vm("--"),O1=Vm("var(--"),Ou=e=>O1(e)?I1.test(e.split("/*")[0].trim()):!1,I1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Im=(e,t)=>t&&typeof e=="number"?t.transform(e):e,vt=(e,t,n)=>n>t?t:n<e?e:n,hr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},di={...hr,transform:e=>vt(0,1,e)},Yi={...hr,default:1},Ti=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Tt=Ti("deg"),it=Ti("%"),L=Ti("px"),F1=Ti("vh"),z1=Ti("vw"),Nf={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},B1={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,backgroundPositionX:L,backgroundPositionY:L},U1={rotate:Tt,rotateX:Tt,rotateY:Tt,rotateZ:Tt,scale:Yi,scaleX:Yi,scaleY:Yi,scaleZ:Yi,skew:Tt,skewX:Tt,skewY:Tt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:di,originX:Nf,originY:Nf,originZ:L},_f={...hr,transform:Math.round},Iu={...B1,...U1,zIndex:_f,size:L,fillOpacity:di,strokeOpacity:di,numOctaves:_f},$1={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},W1=dr.length;function H1(e,t,n){let r="",i=!0;for(let s=0;s<W1;s++){const o=dr[s],l=e[o];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(o.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=Im(l,Iu[o]);if(!a){i=!1;const c=$1[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function Fu(e,t,n){const{style:r,vars:i,transformOrigin:s}=e;let o=!1,l=!1;for(const a in t){const u=t[a];if(Tn.has(a)){o=!0;continue}else if(Om(a)){i[a]=u;continue}else{const c=Im(u,Iu[a]);a.startsWith("origin")?(l=!0,s[a]=c):r[a]=c}}if(t.transform||(o||n?r.transform=H1(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${a} ${u} ${c}`}}const b1={offset:"stroke-dashoffset",array:"stroke-dasharray"},K1={offset:"strokeDashoffset",array:"strokeDasharray"};function G1(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?b1:K1;e[s.offset]=L.transform(-r);const o=L.transform(t),l=L.transform(n);e[s.array]=`${o} ${l}`}function Vf(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function Q1(e,t,n){const r=Vf(t,e.x,e.width),i=Vf(n,e.y,e.height);return`${r} ${i}`}function zu(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:l=1,pathOffset:a=0,...u},c,f){if(Fu(e,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:d,style:g,dimensions:y}=e;d.transform&&(y&&(g.transform=d.transform),delete d.transform),y&&(i!==void 0||s!==void 0||g.transform)&&(g.transformOrigin=Q1(y,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),r!==void 0&&(d.scale=r),o!==void 0&&G1(d,o,l,a,!1)}const Bu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Fm=()=>({...Bu(),attrs:{}}),Uu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function zm(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const Bm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Um(e,t,n,r){zm(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Bm.has(i)?i:Du(i),t.attrs[i])}const $s={};function Y1(e){Object.assign($s,e)}function $m(e,{layout:t,layoutId:n}){return Tn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!$s[e]||e==="opacity")}function $u(e,t,n){var r;const{style:i}=e,s={};for(const o in i)(me(i[o])||t.style&&me(t.style[o])||$m(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}function Wm(e,t,n){const r=$u(e,t,n);for(const i in e)if(me(e[i])||me(t[i])){const s=dr.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[s]=e[i]}return r}function X1(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const Of=["x","y","width","height","cx","cy","r"],Z1={useVisualState:_m({scrapeMotionValuesFromProps:Wm,createRenderState:Fm,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let s=!!e.drag;if(!s){for(const l in i)if(Tn.has(l)){s=!0;break}}if(!s)return;let o=!t;if(t)for(let l=0;l<Of.length;l++){const a=Of[l];e[a]!==t[a]&&(o=!0)}o&&U.read(()=>{X1(n,r),U.render(()=>{zu(r,i,Uu(n.tagName),e.transformTemplate),Um(n,r)})})}})},q1={useVisualState:_m({scrapeMotionValuesFromProps:$u,createRenderState:Bu})};function Hm(e,t,n){for(const r in t)!me(t[r])&&!$m(r,n)&&(e[r]=t[r])}function J1({transformTemplate:e},t){return S.useMemo(()=>{const n=Bu();return Fu(n,t,e),Object.assign({},n.vars,n.style)},[t])}function ew(e,t){const n=e.style||{},r={};return Hm(r,n,e),Object.assign(r,J1(e,t)),r}function tw(e,t){const n={},r=ew(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function nw(e,t,n,r){const i=S.useMemo(()=>{const s=Fm();return zu(s,t,Uu(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Hm(s,e.style,e),i.style={...s,...i.style}}return i}function rw(e=!1){return(n,r,i,{latestValues:s},o)=>{const a=(_u(n)?nw:tw)(r,s,o,n),u=v1(r,typeof n=="string",e),c=n!==S.Fragment?{...u,...a,ref:i}:{},{children:f}=r,d=S.useMemo(()=>me(f)?f.get():f,[f]);return S.createElement(n,{...c,children:d})}}function iw(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={..._u(r)?Z1:q1,preloadedFeatures:e,useRender:rw(i),createVisualElement:t,Component:r};return A1(o)}}function bm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function wo(e,t,n){const r=e.getProps();return Vu(r,t,n!==void 0?n:r.custom,e)}const sw=ju(()=>window.ScrollTimeline!==void 0);class ow{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(sw()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,s)=>{i&&i(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class lw extends ow{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function Wu(e,t){return e?e[t]||e.default||e:void 0}const ga=2e4;function Km(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<ga;)t+=n,r=e.next(t);return t>=ga?1/0:t}function Hu(e){return typeof e=="function"}function If(e,t){e.timeline=t,e.onfinish=null}const bu=e=>Array.isArray(e)&&typeof e[0]=="number",aw={linearEasing:void 0};function uw(e,t){const n=ju(e);return()=>{var r;return(r=aw[t])!==null&&r!==void 0?r:n()}}const Ws=uw(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Gm=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let s=0;s<i;s++)r+=e(ir(0,i-1,s))+", ";return`linear(${r.substring(0,r.length-2)})`};function Qm(e){return!!(typeof e=="function"&&Ws()||!e||typeof e=="string"&&(e in ya||Ws())||bu(e)||Array.isArray(e)&&e.every(Qm))}const Mr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ya={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Mr([0,.65,.55,1]),circOut:Mr([.55,0,1,.45]),backIn:Mr([.31,.01,.66,-.59]),backOut:Mr([.33,1.53,.69,.99])};function Ym(e,t){if(e)return typeof e=="function"&&Ws()?Gm(e,t):bu(e)?Mr(e):Array.isArray(e)?e.map(n=>Ym(n,t)||ya.easeOut):ya[e]}const He={x:!1,y:!1};function Xm(){return He.x||He.y}function cw(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;const s=(r=void 0)!==null&&r!==void 0?r:i.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}function Zm(e,t){const n=cw(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function Ff(e){return t=>{t.pointerType==="touch"||Xm()||e(t)}}function fw(e,t,n={}){const[r,i,s]=Zm(e,n),o=Ff(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const c=Ff(f=>{u(f),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,i)});return r.forEach(l=>{l.addEventListener("pointerenter",o,i)}),s}const qm=(e,t)=>t?e===t?!0:qm(e,t.parentElement):!1,Ku=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,dw=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function hw(e){return dw.has(e.tagName)||e.tabIndex!==-1}const Lr=new WeakSet;function zf(e){return t=>{t.key==="Enter"&&e(t)}}function el(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const pw=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=zf(()=>{if(Lr.has(n))return;el(n,"down");const i=zf(()=>{el(n,"up")}),s=()=>el(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Bf(e){return Ku(e)&&!Xm()}function mw(e,t,n={}){const[r,i,s]=Zm(e,n),o=l=>{const a=l.currentTarget;if(!Bf(l)||Lr.has(a))return;Lr.add(a);const u=t(l),c=(g,y)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",d),!(!Bf(g)||!Lr.has(a))&&(Lr.delete(a),typeof u=="function"&&u(g,{success:y}))},f=g=>{c(g,n.useGlobalTarget||qm(a,g.target))},d=g=>{c(g,!1)};window.addEventListener("pointerup",f,i),window.addEventListener("pointercancel",d,i)};return r.forEach(l=>{!hw(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",o,i),l.addEventListener("focus",u=>pw(u,i),i)}),s}function gw(e){return e==="x"||e==="y"?He[e]?null:(He[e]=!0,()=>{He[e]=!1}):He.x||He.y?null:(He.x=He.y=!0,()=>{He.x=He.y=!1})}const Jm=new Set(["width","height","top","left","right","bottom",...dr]);let ds;function yw(){ds=void 0}const st={now:()=>(ds===void 0&&st.set(ae.isProcessing||d1.useManualTiming?ae.timestamp:performance.now()),ds),set:e=>{ds=e,queueMicrotask(yw)}};function Gu(e,t){e.indexOf(t)===-1&&e.push(t)}function Qu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Yu{constructor(){this.subscriptions=[]}add(t){return Gu(this.subscriptions,t),()=>Qu(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function eg(e,t){return t?e*(1e3/t):0}const Uf=30,vw=e=>!isNaN(parseFloat(e));class xw{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const s=st.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=st.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=vw(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Yu);const r=this.events[t].add(n);return t==="change"?()=>{r(),U.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=st.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Uf)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Uf);return eg(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function hi(e,t){return new xw(e,t)}function ww(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,hi(n))}function Sw(e,t){const n=wo(e,t);let{transitionEnd:r={},transition:i={},...s}=n||{};s={...s,...r};for(const o in s){const l=N1(s[o]);ww(e,o,l)}}function Tw(e){return!!(me(e)&&e.add)}function va(e,t){const n=e.getValue("willChange");if(Tw(n))return n.add(t)}function tg(e){return e.props[Lm]}const ng=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Cw=1e-7,Pw=12;function kw(e,t,n,r,i){let s,o,l=0;do o=t+(n-t)/2,s=ng(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>Cw&&++l<Pw);return o}function Ci(e,t,n,r){if(e===t&&n===r)return je;const i=s=>kw(s,0,1,e,n);return s=>s===0||s===1?s:ng(i(s),t,r)}const rg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ig=e=>t=>1-e(1-t),sg=Ci(.33,1.53,.69,.99),Xu=ig(sg),og=rg(Xu),lg=e=>(e*=2)<1?.5*Xu(e):.5*(2-Math.pow(2,-10*(e-1))),Zu=e=>1-Math.sin(Math.acos(e)),ag=ig(Zu),ug=rg(Zu),cg=e=>/^0[^.\s]+$/u.test(e);function Ew(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||cg(e):!0}const $r=e=>Math.round(e*1e5)/1e5,qu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Aw(e){return e==null}const Rw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ju=(e,t)=>n=>!!(typeof n=="string"&&Rw.test(n)&&n.startsWith(e)||t&&!Aw(n)&&Object.prototype.hasOwnProperty.call(n,t)),fg=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,s,o,l]=r.match(qu);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},jw=e=>vt(0,255,e),tl={...hr,transform:e=>Math.round(jw(e))},an={test:Ju("rgb","red"),parse:fg("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+tl.transform(e)+", "+tl.transform(t)+", "+tl.transform(n)+", "+$r(di.transform(r))+")"};function Mw(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const xa={test:Ju("#"),parse:Mw,transform:an.transform},Fn={test:Ju("hsl","hue"),parse:fg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform($r(t))+", "+it.transform($r(n))+", "+$r(di.transform(r))+")"},he={test:e=>an.test(e)||xa.test(e)||Fn.test(e),parse:e=>an.test(e)?an.parse(e):Fn.test(e)?Fn.parse(e):xa.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?an.transform(e):Fn.transform(e)},Lw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Dw(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(qu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lw))===null||n===void 0?void 0:n.length)||0)>0}const dg="number",hg="color",Nw="var",_w="var(",$f="${}",Vw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function pi(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let s=0;const l=t.replace(Vw,a=>(he.test(a)?(r.color.push(s),i.push(hg),n.push(he.parse(a))):a.startsWith(_w)?(r.var.push(s),i.push(Nw),n.push(a)):(r.number.push(s),i.push(dg),n.push(parseFloat(a))),++s,$f)).split($f);return{values:n,split:l,indexes:r,types:i}}function pg(e){return pi(e).values}function mg(e){const{split:t,types:n}=pi(e),r=t.length;return i=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],i[o]!==void 0){const l=n[o];l===dg?s+=$r(i[o]):l===hg?s+=he.transform(i[o]):s+=i[o]}return s}}const Ow=e=>typeof e=="number"?0:e;function Iw(e){const t=pg(e);return mg(e)(t.map(Ow))}const Wt={test:Dw,parse:pg,createTransformer:mg,getAnimatableNone:Iw},Fw=new Set(["brightness","contrast","saturate","opacity"]);function zw(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(qu)||[];if(!r)return e;const i=n.replace(r,"");let s=Fw.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Bw=/\b([a-z-]*)\(.*?\)/gu,wa={...Wt,getAnimatableNone:e=>{const t=e.match(Bw);return t?t.map(zw).join(" "):e}},Uw={...Iu,color:he,backgroundColor:he,outlineColor:he,fill:he,stroke:he,borderColor:he,borderTopColor:he,borderRightColor:he,borderBottomColor:he,borderLeftColor:he,filter:wa,WebkitFilter:wa},ec=e=>Uw[e];function gg(e,t){let n=ec(e);return n!==wa&&(n=Wt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const $w=new Set(["auto","none","0"]);function Ww(e,t,n){let r=0,i;for(;r<e.length&&!i;){const s=e[r];typeof s=="string"&&!$w.has(s)&&pi(s).values.length&&(i=e[r]),r++}if(i&&n)for(const s of t)e[s]=gg(n,i)}const Wf=e=>e===hr||e===L,Hf=(e,t)=>parseFloat(e.split(", ")[t]),bf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return Hf(i[1],t);{const s=r.match(/^matrix\((.+)\)$/u);return s?Hf(s[1],e):0}},Hw=new Set(["x","y","z"]),bw=dr.filter(e=>!Hw.has(e));function Kw(e){const t=[];return bw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const or={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:bf(4,13),y:bf(5,14)};or.translateX=or.x;or.translateY=or.y;const dn=new Set;let Sa=!1,Ta=!1;function yg(){if(Ta){const e=Array.from(dn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=Kw(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([s,o])=>{var l;(l=r.getValue(s))===null||l===void 0||l.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Ta=!1,Sa=!1,dn.forEach(e=>e.complete()),dn.clear()}function vg(){dn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Ta=!0)})}function Gw(){vg(),yg()}class tc{constructor(t,n,r,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(dn.add(this),Sa||(Sa=!0,U.read(vg),U.resolveKeyframes(yg))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const o=i==null?void 0:i.get(),l=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),i&&o===void 0&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),dn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,dn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const xg=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Qw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Yw(e){const t=Qw.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function wg(e,t,n=1){const[r,i]=Yw(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return xg(o)?parseFloat(o):o}return Ou(i)?wg(i,t,n+1):i}const Sg=e=>t=>t.test(e),Xw={test:e=>e==="auto",parse:e=>e},Tg=[hr,L,it,Tt,z1,F1,Xw],Kf=e=>Tg.find(Sg(e));class Cg extends tc{constructor(t,n,r,i,s){super(t,n,r,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),Ou(u))){const c=wg(u,n.current);c!==void 0&&(t[a]=c),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Jm.has(r)||t.length!==2)return;const[i,s]=t,o=Kf(i),l=Kf(s);if(o!==l)if(Wf(o)&&Wf(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)Ew(t[i])&&r.push(i);r.length&&Ww(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=or[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,l=i[o];i[o]=or[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const Gf=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Wt.test(e)||e==="0")&&!e.startsWith("url("));function Zw(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function qw(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],o=Gf(i,t),l=Gf(s,t);return!o||!l?!1:Zw(e)||(n==="spring"||Hu(n))&&r}const Jw=e=>e!==null;function So(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(Jw),s=t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const eS=40;class Pg{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=st.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:s,repeatType:o,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>eS?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Gw(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=st.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:s,delay:o,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!qw(t,r,i,s))if(o)this.options.duration=0;else{a&&a(So(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const H=(e,t,n)=>e+(t-e)*n;function nl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function tS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=nl(a,l,e+1/3),s=nl(a,l,e),o=nl(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}function Hs(e,t){return n=>n>0?t:e}const rl=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},nS=[xa,an,Fn],rS=e=>nS.find(t=>t.test(e));function Qf(e){const t=rS(e);if(!t)return!1;let n=t.parse(e);return t===Fn&&(n=tS(n)),n}const Yf=(e,t)=>{const n=Qf(e),r=Qf(t);if(!n||!r)return Hs(e,t);const i={...n};return s=>(i.red=rl(n.red,r.red,s),i.green=rl(n.green,r.green,s),i.blue=rl(n.blue,r.blue,s),i.alpha=H(n.alpha,r.alpha,s),an.transform(i))},iS=(e,t)=>n=>t(e(n)),Pi=(...e)=>e.reduce(iS),Ca=new Set(["none","hidden"]);function sS(e,t){return Ca.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function oS(e,t){return n=>H(e,t,n)}function nc(e){return typeof e=="number"?oS:typeof e=="string"?Ou(e)?Hs:he.test(e)?Yf:uS:Array.isArray(e)?kg:typeof e=="object"?he.test(e)?Yf:lS:Hs}function kg(e,t){const n=[...e],r=n.length,i=e.map((s,o)=>nc(s)(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}}function lS(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=nc(e[i])(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}}function aS(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],l=e.indexes[o][i[o]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[s]=a,i[o]++}return r}const uS=(e,t)=>{const n=Wt.createTransformer(t),r=pi(e),i=pi(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Ca.has(e)&&!i.values.length||Ca.has(t)&&!r.values.length?sS(e,t):Pi(kg(aS(r,i),i.values),n):Hs(e,t)};function Eg(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?H(e,t,n):nc(e)(e,t)}const cS=5;function Ag(e,t,n){const r=Math.max(t-cS,0);return eg(n-e(r),t-r)}const G={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},il=.001;function fS({duration:e=G.duration,bounce:t=G.bounce,velocity:n=G.velocity,mass:r=G.mass}){let i,s,o=1-t;o=vt(G.minDamping,G.maxDamping,o),e=vt(G.minDuration,G.maxDuration,ht(e)),o<1?(i=u=>{const c=u*o,f=c*e,d=c-n,g=Pa(u,o),y=Math.exp(-f);return il-d/g*y},s=u=>{const f=u*o*e,d=f*n+n,g=Math.pow(o,2)*Math.pow(u,2)*e,y=Math.exp(-f),x=Pa(Math.pow(u,2),o);return(-i(u)+il>0?-1:1)*((d-g)*y)/x}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-il+c*f},s=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=hS(i,s,l);if(e=dt(e),isNaN(a))return{stiffness:G.stiffness,damping:G.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const dS=12;function hS(e,t,n){let r=n;for(let i=1;i<dS;i++)r=r-e(r)/t(r);return r}function Pa(e,t){return e*Math.sqrt(1-t*t)}const pS=["duration","bounce"],mS=["stiffness","damping","mass"];function Xf(e,t){return t.some(n=>e[n]!==void 0)}function gS(e){let t={velocity:G.velocity,stiffness:G.stiffness,damping:G.damping,mass:G.mass,isResolvedFromDuration:!1,...e};if(!Xf(e,mS)&&Xf(e,pS))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,s=2*vt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:G.mass,stiffness:i,damping:s}}else{const n=fS(e);t={...t,...n,mass:G.mass},t.isResolvedFromDuration=!0}return t}function Rg(e=G.visualDuration,t=G.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const s=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:a,damping:u,mass:c,duration:f,velocity:d,isResolvedFromDuration:g}=gS({...n,velocity:-ht(n.velocity||0)}),y=d||0,x=u/(2*Math.sqrt(a*c)),T=o-s,p=ht(Math.sqrt(a/c)),h=Math.abs(T)<5;r||(r=h?G.restSpeed.granular:G.restSpeed.default),i||(i=h?G.restDelta.granular:G.restDelta.default);let m;if(x<1){const C=Pa(p,x);m=k=>{const A=Math.exp(-x*p*k);return o-A*((y+x*p*T)/C*Math.sin(C*k)+T*Math.cos(C*k))}}else if(x===1)m=C=>o-Math.exp(-p*C)*(T+(y+p*T)*C);else{const C=p*Math.sqrt(x*x-1);m=k=>{const A=Math.exp(-x*p*k),P=Math.min(C*k,300);return o-A*((y+x*p*T)*Math.sinh(P)+C*T*Math.cosh(P))/C}}const w={calculatedDuration:g&&f||null,next:C=>{const k=m(C);if(g)l.done=C>=f;else{let A=0;x<1&&(A=C===0?dt(y):Ag(m,C,k));const P=Math.abs(A)<=r,_=Math.abs(o-k)<=i;l.done=P&&_}return l.value=l.done?o:k,l},toString:()=>{const C=Math.min(Km(w),ga),k=Gm(A=>w.next(C*A).value,C,30);return C+"ms "+k}};return w}function Zf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,y=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let x=n*t;const T=f+x,p=o===void 0?T:o(T);p!==T&&(x=p-f);const h=P=>-x*Math.exp(-P/r),m=P=>p+h(P),w=P=>{const _=h(P),M=m(P);d.done=Math.abs(_)<=u,d.value=d.done?p:M};let C,k;const A=P=>{g(d.value)&&(C=P,k=Rg({keyframes:[d.value,y(d.value)],velocity:Ag(m,P,d.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return A(0),{calculatedDuration:null,next:P=>{let _=!1;return!k&&C===void 0&&(_=!0,w(P),A(P)),C!==void 0&&P>=C?k.next(P-C):(!_&&w(P),d)}}}const yS=Ci(.42,0,1,1),vS=Ci(0,0,.58,1),jg=Ci(.42,0,.58,1),xS=e=>Array.isArray(e)&&typeof e[0]!="number",wS={linear:je,easeIn:yS,easeInOut:jg,easeOut:vS,circIn:Zu,circInOut:ug,circOut:ag,backIn:Xu,backInOut:og,backOut:sg,anticipate:lg},qf=e=>{if(bu(e)){Em(e.length===4);const[t,n,r,i]=e;return Ci(t,n,r,i)}else if(typeof e=="string")return wS[e];return e};function SS(e,t,n){const r=[],i=n||Eg,s=e.length-1;for(let o=0;o<s;o++){let l=i(e[o],e[o+1]);if(t){const a=Array.isArray(t)?t[o]||je:t;l=Pi(a,l)}r.push(l)}return r}function TS(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(Em(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=SS(t,r,i),a=l.length,u=c=>{if(o&&c<e[0])return t[0];let f=0;if(a>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const d=ir(e[f],e[f+1],c);return l[f](d)};return n?c=>u(vt(e[0],e[s-1],c)):u}function CS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=ir(0,t,r);e.push(H(n,1,i))}}function PS(e){const t=[0];return CS(t,e.length-1),t}function kS(e,t){return e.map(n=>n*t)}function ES(e,t){return e.map(()=>t||jg).splice(0,e.length-1)}function bs({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=xS(r)?r.map(qf):qf(r),s={done:!1,value:t[0]},o=kS(n&&n.length===t.length?n:PS(t),e),l=TS(o,t,{ease:Array.isArray(i)?i:ES(t,i)});return{calculatedDuration:e,next:a=>(s.value=l(a),s.done=a>=e,s)}}const AS=e=>{const t=({timestamp:n})=>e(n);return{start:()=>U.update(t,!0),stop:()=>$t(t),now:()=>ae.isProcessing?ae.timestamp:st.now()}},RS={decay:Zf,inertia:Zf,tween:bs,keyframes:bs,spring:Rg},jS=e=>e/100;class rc extends Pg{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:i,keyframes:s}=this.options,o=(i==null?void 0:i.KeyframeResolver)||tc,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new o(s,l,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,l=Hu(n)?n:RS[n]||bs;let a,u;l!==bs&&typeof t[0]!="number"&&(a=Pi(jS,Eg(t[0],t[1])),t=[0,100]);const c=l({...this.options,keyframes:t});s==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=Km(c));const{calculatedDuration:f}=c,d=f+i,g=d*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:f,resolvedDuration:d,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:c,resolvedDuration:f}=r;if(this.startTime===null)return s.next(0);const{delay:d,repeat:g,repeatType:y,repeatDelay:x,onUpdate:T}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-d*(this.speed>=0?1:-1),h=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let m=this.currentTime,w=s;if(g){const P=Math.min(this.currentTime,c)/f;let _=Math.floor(P),M=P%1;!M&&P>=1&&(M=1),M===1&&_--,_=Math.min(_,g+1),!!(_%2)&&(y==="reverse"?(M=1-M,x&&(M-=x/f)):y==="mirror"&&(w=o)),m=vt(0,1,M)*f}const C=h?{done:!1,value:a[0]}:w.next(m);l&&(C.value=l(C.value));let{done:k}=C;!h&&u!==null&&(k=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const A=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return A&&i!==void 0&&(C.value=So(a,this.options,i)),T&&T(C.value),A&&this.finish(),C}get duration(){const{resolved:t}=this;return t?ht(t.calculatedDuration):0}get time(){return ht(this.currentTime)}set time(t){t=dt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ht(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=AS,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const MS=new Set(["opacity","clipPath","filter","transform"]);function LS(e,t,n,{delay:r=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=Ym(l,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}const DS=ju(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ks=10,NS=2e4;function _S(e){return Hu(e.type)||e.type==="spring"||!Qm(e.ease)}function VS(e,t){const n=new rc({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let s=0;for(;!r.done&&s<NS;)r=n.sample(s),i.push(r.value),s+=Ks;return{times:void 0,keyframes:i,duration:s-Ks,ease:"linear"}}const Mg={anticipate:lg,backInOut:og,circInOut:ug};function OS(e){return e in Mg}class Jf extends Pg{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:s}=this.options;this.resolver=new Cg(s,(o,l)=>this.onKeyframesResolved(o,l),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:s,type:o,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof s=="string"&&Ws()&&OS(s)&&(s=Mg[s]),_S(this.options)){const{onComplete:f,onUpdate:d,motionValue:g,element:y,...x}=this.options,T=VS(t,x);t=T.keyframes,t.length===1&&(t[1]=t[0]),r=T.duration,i=T.times,s=T.ease,o="keyframes"}const c=LS(l.owner.current,a,t,{...this.options,duration:r,times:i,ease:s});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(If(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(So(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ht(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ht(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=dt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return je;const{animation:r}=n;If(r,t)}return je}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:s,ease:o,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:f,element:d,...g}=this.options,y=new rc({...g,keyframes:r,duration:i,type:s,ease:o,times:l,isGenerator:!0}),x=dt(this.time);u.setWithVelocity(y.sample(x-Ks).value,y.sample(x).value,Ks)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:s,damping:o,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return DS()&&r&&MS.has(r)&&!a&&!u&&!i&&s!=="mirror"&&o!==0&&l!=="inertia"}}const IS={type:"spring",stiffness:500,damping:25,restSpeed:10},FS=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),zS={type:"keyframes",duration:.8},BS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},US=(e,{keyframes:t})=>t.length>2?zS:Tn.has(e)?e.startsWith("scale")?FS(t[1]):IS:BS;function $S({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}const ic=(e,t,n,r={},i,s)=>o=>{const l=Wu(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-dt(a);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:d=>{t.set(d),l.onUpdate&&l.onUpdate(d)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:s?void 0:i};$S(l)||(c={...c,...US(e,c)}),c.duration&&(c.duration=dt(c.duration)),c.repeatDelay&&(c.repeatDelay=dt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!s&&t.get()!==void 0){const d=So(c.keyframes,l);if(d!==void 0)return U.update(()=>{c.onUpdate(d),c.onComplete()}),new lw([])}return!s&&Jf.supports(c)?new Jf(c):new rc(c)};function WS({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Lg(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in a){const d=e.getValue(f,(s=e.latestValues[f])!==null&&s!==void 0?s:null),g=a[f];if(g===void 0||c&&WS(c,f))continue;const y={delay:n,...Wu(o||{},f)};let x=!1;if(window.MotionHandoffAnimation){const p=tg(e);if(p){const h=window.MotionHandoffAnimation(p,f,U);h!==null&&(y.startTime=h,x=!0)}}va(e,f),d.start(ic(f,d,g,e.shouldReduceMotion&&Jm.has(f)?{type:!1}:y,e,x));const T=d.animation;T&&u.push(T)}return l&&Promise.all(u).then(()=>{U.update(()=>{l&&Sw(e,l)})}),u}function ka(e,t,n={}){var r;const i=wo(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Lg(e,i,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=s;return HS(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[u,c]=a==="beforeChildren"?[o,l]:[l,o];return u().then(()=>c())}else return Promise.all([o(),l(n.delay)])}function HS(e,t,n=0,r=0,i=1,s){const o=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(bS).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(ka(u,t,{...s,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function bS(e,t){return e.sortNodePosition(t)}function KS(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>ka(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=ka(e,t,n);else{const i=typeof t=="function"?wo(e,t,n.custom):t;r=Promise.all(Lg(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const GS=Lu.length;function Dg(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Dg(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<GS;n++){const r=Lu[n],i=e.props[r];(fi(i)||i===!1)&&(t[r]=i)}return t}const QS=[...Mu].reverse(),YS=Mu.length;function XS(e){return t=>Promise.all(t.map(({animation:n,options:r})=>KS(e,n,r)))}function ZS(e){let t=XS(e),n=ed(),r=!0;const i=a=>(u,c)=>{var f;const d=wo(e,c,a==="exit"?(f=e.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(d){const{transition:g,transitionEnd:y,...x}=d;u={...u,...x,...y}}return u};function s(a){t=a(e)}function o(a){const{props:u}=e,c=Dg(e.parent)||{},f=[],d=new Set;let g={},y=1/0;for(let T=0;T<YS;T++){const p=QS[T],h=n[p],m=u[p]!==void 0?u[p]:c[p],w=fi(m),C=p===a?h.isActive:null;C===!1&&(y=T);let k=m===c[p]&&m!==u[p]&&w;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),h.protectedKeys={...g},!h.isActive&&C===null||!m&&!h.prevProp||vo(m)||typeof m=="boolean")continue;const A=qS(h.prevProp,m);let P=A||p===a&&h.isActive&&!k&&w||T>y&&w,_=!1;const M=Array.isArray(m)?m:[m];let J=M.reduce(i(p),{});C===!1&&(J={});const{prevResolvedValues:wt={}}=h,Yt={...wt,...J},pr=te=>{P=!0,d.has(te)&&(_=!0,d.delete(te)),h.needsAnimating[te]=!0;const R=e.getValue(te);R&&(R.liveStyle=!1)};for(const te in Yt){const R=J[te],D=wt[te];if(g.hasOwnProperty(te))continue;let N=!1;ma(R)&&ma(D)?N=!bm(R,D):N=R!==D,N?R!=null?pr(te):d.add(te):R!==void 0&&d.has(te)?pr(te):h.protectedKeys[te]=!0}h.prevProp=m,h.prevResolvedValues=J,h.isActive&&(g={...g,...J}),r&&e.blockInitialAnimation&&(P=!1),P&&(!(k&&A)||_)&&f.push(...M.map(te=>({animation:te,options:{type:p}})))}if(d.size){const T={};d.forEach(p=>{const h=e.getBaseTarget(p),m=e.getValue(p);m&&(m.liveStyle=!0),T[p]=h??null}),f.push({animation:T})}let x=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(f):Promise.resolve()}function l(a,u){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(d=>{var g;return(g=d.animationState)===null||g===void 0?void 0:g.setActive(a,u)}),n[a].isActive=u;const f=o(a);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:o,setActive:l,setAnimateFunction:s,getState:()=>n,reset:()=>{n=ed(),r=!0}}}function qS(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!bm(t,e):!1}function qt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ed(){return{animate:qt(!0),whileInView:qt(),whileHover:qt(),whileTap:qt(),whileDrag:qt(),whileFocus:qt(),exit:qt()}}class Qt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class JS extends Qt{constructor(t){super(t),t.animationState||(t.animationState=ZS(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();vo(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let eT=0;class tT extends Qt{constructor(){super(...arguments),this.id=eT++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const nT={animation:{Feature:JS},exit:{Feature:tT}};function mi(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function ki(e){return{point:{x:e.pageX,y:e.pageY}}}const rT=e=>t=>Ku(t)&&e(t,ki(t));function Wr(e,t,n,r){return mi(e,t,rT(n),r)}const td=(e,t)=>Math.abs(e-t);function iT(e,t){const n=td(e.x,t.x),r=td(e.y,t.y);return Math.sqrt(n**2+r**2)}class Ng{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=ol(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,g=iT(f.offset,{x:0,y:0})>=3;if(!d&&!g)return;const{point:y}=f,{timestamp:x}=ae;this.history.push({...y,timestamp:x});const{onStart:T,onMove:p}=this.handlers;d||(T&&T(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=sl(d,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:g,onSessionEnd:y,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const T=ol(f.type==="pointercancel"?this.lastMoveEventInfo:sl(d,this.transformPagePoint),this.history);this.startEvent&&g&&g(f,T),y&&y(f,T)},!Ku(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=ki(t),l=sl(o,this.transformPagePoint),{point:a}=l,{timestamp:u}=ae;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ol(l,this.history)),this.removeListeners=Pi(Wr(this.contextWindow,"pointermove",this.handlePointerMove),Wr(this.contextWindow,"pointerup",this.handlePointerUp),Wr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),$t(this.updatePoint)}}function sl(e,t){return t?{point:t(e.point)}:e}function nd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ol({point:e},t){return{point:e,delta:nd(e,_g(t)),offset:nd(e,sT(t)),velocity:oT(t,.1)}}function sT(e){return e[0]}function _g(e){return e[e.length-1]}function oT(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=_g(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>dt(t)));)n--;if(!r)return{x:0,y:0};const s=ht(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const Vg=1e-4,lT=1-Vg,aT=1+Vg,Og=.01,uT=0-Og,cT=0+Og;function Le(e){return e.max-e.min}function fT(e,t,n){return Math.abs(e-t)<=n}function rd(e,t,n,r=.5){e.origin=r,e.originPoint=H(t.min,t.max,e.origin),e.scale=Le(n)/Le(t),e.translate=H(n.min,n.max,e.origin)-e.originPoint,(e.scale>=lT&&e.scale<=aT||isNaN(e.scale))&&(e.scale=1),(e.translate>=uT&&e.translate<=cT||isNaN(e.translate))&&(e.translate=0)}function Hr(e,t,n,r){rd(e.x,t.x,n.x,r?r.originX:void 0),rd(e.y,t.y,n.y,r?r.originY:void 0)}function id(e,t,n){e.min=n.min+t.min,e.max=e.min+Le(t)}function dT(e,t,n){id(e.x,t.x,n.x),id(e.y,t.y,n.y)}function sd(e,t,n){e.min=t.min-n.min,e.max=e.min+Le(t)}function br(e,t,n){sd(e.x,t.x,n.x),sd(e.y,t.y,n.y)}function hT(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?H(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?H(n,e,r.max):Math.min(e,n)),e}function od(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function pT(e,{top:t,left:n,bottom:r,right:i}){return{x:od(e.x,n,i),y:od(e.y,t,r)}}function ld(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function mT(e,t){return{x:ld(e.x,t.x),y:ld(e.y,t.y)}}function gT(e,t){let n=.5;const r=Le(e),i=Le(t);return i>r?n=ir(t.min,t.max-r,e.min):r>i&&(n=ir(e.min,e.max-i,t.min)),vt(0,1,n)}function yT(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ea=.35;function vT(e=Ea){return e===!1?e=0:e===!0&&(e=Ea),{x:ad(e,"left","right"),y:ad(e,"top","bottom")}}function ad(e,t,n){return{min:ud(e,t),max:ud(e,n)}}function ud(e,t){return typeof e=="number"?e:e[t]||0}const cd=()=>({translate:0,scale:1,origin:0,originPoint:0}),zn=()=>({x:cd(),y:cd()}),fd=()=>({min:0,max:0}),Z=()=>({x:fd(),y:fd()});function Ve(e){return[e("x"),e("y")]}function Ig({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function xT({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function wT(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ll(e){return e===void 0||e===1}function Aa({scale:e,scaleX:t,scaleY:n}){return!ll(e)||!ll(t)||!ll(n)}function nn(e){return Aa(e)||Fg(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Fg(e){return dd(e.x)||dd(e.y)}function dd(e){return e&&e!=="0%"}function Gs(e,t,n){const r=e-n,i=t*r;return n+i}function hd(e,t,n,r,i){return i!==void 0&&(e=Gs(e,i,r)),Gs(e,n,r)+t}function Ra(e,t=0,n=1,r,i){e.min=hd(e.min,t,n,r,i),e.max=hd(e.max,t,n,r,i)}function zg(e,{x:t,y:n}){Ra(e.x,t.translate,t.scale,t.originPoint),Ra(e.y,n.translate,n.scale,n.originPoint)}const pd=.999999999999,md=1.0000000000001;function ST(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let l=0;l<i;l++){s=n[l],o=s.projectionDelta;const{visualElement:a}=s.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&Un(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,zg(e,o)),r&&nn(s.latestValues)&&Un(e,s.latestValues))}t.x<md&&t.x>pd&&(t.x=1),t.y<md&&t.y>pd&&(t.y=1)}function Bn(e,t){e.min=e.min+t,e.max=e.max+t}function gd(e,t,n,r,i=.5){const s=H(e.min,e.max,i);Ra(e,t,n,s,r)}function Un(e,t){gd(e.x,t.x,t.scaleX,t.scale,t.originX),gd(e.y,t.y,t.scaleY,t.scale,t.originY)}function Bg(e,t){return Ig(wT(e.getBoundingClientRect(),t))}function TT(e,t,n){const r=Bg(e,n),{scroll:i}=t;return i&&(Bn(r.x,i.offset.x),Bn(r.y,i.offset.y)),r}const Ug=({current:e})=>e?e.ownerDocument.defaultView:null,CT=new WeakMap;class PT{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Z(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ki(c).point)},s=(c,f)=>{const{drag:d,dragPropagation:g,onDragStart:y}=this.getProps();if(d&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=gw(d),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ve(T=>{let p=this.getAxisMotionValue(T).get()||0;if(it.test(p)){const{projection:h}=this.visualElement;if(h&&h.layout){const m=h.layout.layoutBox[T];m&&(p=Le(m)*(parseFloat(p)/100))}}this.originPoint[T]=p}),y&&U.postRender(()=>y(c,f)),va(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(c,f)=>{const{dragPropagation:d,dragDirectionLock:g,onDirectionLock:y,onDrag:x}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:T}=f;if(g&&this.currentDirection===null){this.currentDirection=kT(T),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",f.point,T),this.updateAxis("y",f.point,T),this.visualElement.render(),x&&x(c,f)},l=(c,f)=>this.stop(c,f),a=()=>Ve(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Ng(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Ug(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&U.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Xi(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=hT(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&In(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=pT(i.layoutBox,n):this.constraints=!1,this.elastic=vT(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ve(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=yT(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!In(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=TT(r,i.root,this.visualElement.getTransformPagePoint());let o=mT(i.layout.layoutBox,s);if(n){const l=n(xT(o));this.hasMutatedConstraints=!!l,l&&(o=Ig(l))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Ve(c=>{if(!Xi(c,n,this.currentDirection))return;let f=a&&a[c]||{};o&&(f={min:0,max:0});const d=i?200:1e6,g=i?40:1e7,y={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...s,...f};return this.startAxisValueAnimation(c,y)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return va(this.visualElement,t),r.start(ic(t,r,0,n,this.visualElement,!1))}stopAnimation(){Ve(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ve(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ve(n=>{const{drag:r}=this.getProps();if(!Xi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:l}=i.layout.layoutBox[n];s.set(t[n]-H(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!In(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ve(o=>{const l=this.getAxisMotionValue(o);if(l&&this.constraints!==!1){const a=l.get();i[o]=gT({min:a,max:a},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ve(o=>{if(!Xi(o,t,null))return;const l=this.getAxisMotionValue(o),{min:a,max:u}=this.constraints[o];l.set(H(a,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;CT.set(this.visualElement,this);const t=this.visualElement.current,n=Wr(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();In(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),U.read(r);const o=mi(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ve(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Ea,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:l}}}function Xi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function kT(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class ET extends Qt{constructor(t){super(t),this.removeGroupControls=je,this.removeListeners=je,this.controls=new PT(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||je}unmount(){this.removeGroupControls(),this.removeListeners()}}const yd=e=>(t,n)=>{e&&U.postRender(()=>e(t,n))};class AT extends Qt{constructor(){super(...arguments),this.removePointerDownListener=je}onPointerDown(t){this.session=new Ng(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ug(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:yd(t),onStart:yd(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&U.postRender(()=>i(s,o))}}}mount(){this.removePointerDownListener=Wr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const hs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function vd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const kr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=vd(e,t.target.x),r=vd(e,t.target.y);return`${n}% ${r}%`}},RT={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Wt.parse(e);if(i.length>5)return r;const s=Wt.createTransformer(e),o=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+o]/=l,i[1+o]/=a;const u=H(l,a,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class jT extends S.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;Y1(MT),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),hs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||U.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Nu.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function $g(e){const[t,n]=Pm(),r=S.useContext(ku);return v.jsx(jT,{...e,layoutGroup:r,switchLayoutGroup:S.useContext(Dm),isPresent:t,safeToRemove:n})}const MT={borderRadius:{...kr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:kr,borderTopRightRadius:kr,borderBottomLeftRadius:kr,borderBottomRightRadius:kr,boxShadow:RT};function LT(e,t,n){const r=me(e)?e:hi(e);return r.start(ic("",r,t,n)),r.animation}function DT(e){return e instanceof SVGElement&&e.tagName!=="svg"}const NT=(e,t)=>e.depth-t.depth;class _T{constructor(){this.children=[],this.isDirty=!1}add(t){Gu(this.children,t),this.isDirty=!0}remove(t){Qu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(NT),this.isDirty=!1,this.children.forEach(t)}}function VT(e,t){const n=st.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&($t(r),e(s-t))};return U.read(r,!0),()=>$t(r)}const Wg=["TopLeft","TopRight","BottomLeft","BottomRight"],OT=Wg.length,xd=e=>typeof e=="string"?parseFloat(e):e,wd=e=>typeof e=="number"||L.test(e);function IT(e,t,n,r,i,s){i?(e.opacity=H(0,n.opacity!==void 0?n.opacity:1,FT(r)),e.opacityExit=H(t.opacity!==void 0?t.opacity:1,0,zT(r))):s&&(e.opacity=H(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<OT;o++){const l=`border${Wg[o]}Radius`;let a=Sd(t,l),u=Sd(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||wd(a)===wd(u)?(e[l]=Math.max(H(xd(a),xd(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=H(t.rotate||0,n.rotate||0,r))}function Sd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const FT=Hg(0,.5,ag),zT=Hg(.5,.95,je);function Hg(e,t,n){return r=>r<e?0:r>t?1:n(ir(e,t,r))}function Td(e,t){e.min=t.min,e.max=t.max}function _e(e,t){Td(e.x,t.x),Td(e.y,t.y)}function Cd(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Pd(e,t,n,r,i){return e-=t,e=Gs(e,1/n,r),i!==void 0&&(e=Gs(e,1/i,r)),e}function BT(e,t=0,n=1,r=.5,i,s=e,o=e){if(it.test(t)&&(t=parseFloat(t),t=H(o.min,o.max,t/100)-o.min),typeof t!="number")return;let l=H(s.min,s.max,r);e===s&&(l-=t),e.min=Pd(e.min,t,n,l,i),e.max=Pd(e.max,t,n,l,i)}function kd(e,t,[n,r,i],s,o){BT(e,t[n],t[r],t[i],t.scale,s,o)}const UT=["x","scaleX","originX"],$T=["y","scaleY","originY"];function Ed(e,t,n,r){kd(e.x,t,UT,n?n.x:void 0,r?r.x:void 0),kd(e.y,t,$T,n?n.y:void 0,r?r.y:void 0)}function Ad(e){return e.translate===0&&e.scale===1}function bg(e){return Ad(e.x)&&Ad(e.y)}function Rd(e,t){return e.min===t.min&&e.max===t.max}function WT(e,t){return Rd(e.x,t.x)&&Rd(e.y,t.y)}function jd(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Kg(e,t){return jd(e.x,t.x)&&jd(e.y,t.y)}function Md(e){return Le(e.x)/Le(e.y)}function Ld(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class HT{constructor(){this.members=[]}add(t){Gu(this.members,t),t.scheduleRender()}remove(t){if(Qu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function bT(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((i||s||o)&&(r=`translate3d(${i}px, ${s}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:d,skewX:g,skewY:y}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),d&&(r+=`rotateY(${d}deg) `),g&&(r+=`skewX(${g}deg) `),y&&(r+=`skewY(${y}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const rn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Dr=typeof window<"u"&&window.MotionDebug!==void 0,al=["","X","Y","Z"],KT={visibility:"hidden"},Dd=1e3;let GT=0;function ul(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Gg(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=tg(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",U,!(i||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Gg(r)}function Qg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},l=t==null?void 0:t()){this.id=GT++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Dr&&(rn.totalNodes=rn.resolvedTargetDeltas=rn.recalculatedProjection=0),this.nodes.forEach(XT),this.nodes.forEach(tC),this.nodes.forEach(nC),this.nodes.forEach(ZT),Dr&&window.MotionDebug.record(rn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new _T)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Yu),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const a=this.eventHandlers.get(o);a&&a.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=DT(o),this.instance=o;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=VT(d,250),hs.hasAnimatedSinceResize&&(hs.hasAnimatedSinceResize=!1,this.nodes.forEach(_d))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||lC,{onLayoutAnimationStart:T,onLayoutAnimationComplete:p}=c.getProps(),h=!this.targetLayout||!Kg(this.targetLayout,y)||g,m=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||d&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,m);const w={...Wu(x,"layout"),onPlay:T,onComplete:p};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else d||_d(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,$t(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rC),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Gg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Nd);return}this.isUpdating||this.nodes.forEach(JT),this.isUpdating=!1,this.nodes.forEach(eC),this.nodes.forEach(QT),this.nodes.forEach(YT),this.clearAllSnapshots();const l=st.now();ae.delta=vt(0,1e3/60,l-ae.timestamp),ae.timestamp=l,ae.isProcessing=!0,Jo.update.process(ae),Jo.preRender.process(ae),Jo.render.process(ae),ae.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Nu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(qT),this.sharedNodes.forEach(iC)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Z(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!bg(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(l||nn(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return o&&(a=this.removeTransform(a)),aC(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:l}=this.options;if(!l)return Z();const a=l.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(uC))){const{scroll:c}=this.root;c&&(Bn(a.x,c.offset.x),Bn(a.y,c.offset.y))}return a}removeElementScroll(o){var l;const a=Z();if(_e(a,o),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:d}=c;c!==this.root&&f&&d.layoutScroll&&(f.wasRoot&&_e(a,o),Bn(a.x,f.offset.x),Bn(a.y,f.offset.y))}return a}applyTransform(o,l=!1){const a=Z();_e(a,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Un(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),nn(c.latestValues)&&Un(a,c.latestValues)}return nn(this.latestValues)&&Un(a,this.latestValues),a}removeTransform(o){const l=Z();_e(l,o);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!nn(u.latestValues))continue;Aa(u.latestValues)&&u.updateSnapshot();const c=Z(),f=u.measurePageBox();_e(c,f),Ed(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return nn(this.latestValues)&&Ed(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ae.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=ae.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),br(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),_e(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Z(),this.targetWithTransforms=Z()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),dT(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_e(this.target,this.layout.layoutBox),zg(this.target,this.targetDelta)):_e(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),br(this.relativeTargetOrigin,this.target,g.target),_e(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Dr&&rn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Aa(this.parent.latestValues)||Fg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===ae.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;_e(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,g=this.treeScale.y;ST(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=Z());const{target:y}=l;if(!y){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Cd(this.prevProjectionDelta.x,this.projectionDelta.x),Cd(this.prevProjectionDelta.y,this.projectionDelta.y)),Hr(this.projectionDelta,this.layoutCorrected,y,this.latestValues),(this.treeScale.x!==d||this.treeScale.y!==g||!Ld(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ld(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),Dr&&rn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=zn(),this.projectionDelta=zn(),this.projectionDeltaWithTransform=zn()}setAnimationOrigin(o,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=zn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=Z(),g=a?a.source:void 0,y=this.layout?this.layout.source:void 0,x=g!==y,T=this.getStack(),p=!T||T.members.length<=1,h=!!(x&&!p&&this.options.crossfade===!0&&!this.path.some(oC));this.animationProgress=0;let m;this.mixTargetDelta=w=>{const C=w/1e3;Vd(f.x,o.x,C),Vd(f.y,o.y,C),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(br(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),sC(this.relativeTarget,this.relativeTargetOrigin,d,C),m&&WT(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=Z()),_e(m,this.relativeTarget)),x&&(this.animationValues=c,IT(c,u,this.latestValues,C,h,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&($t(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{hs.hasAnimatedSinceResize=!0,this.currentAnimation=LT(0,Dd,{...o,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Dd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=o;if(!(!l||!a||!u)){if(this!==o&&this.layout&&u&&Yg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||Z();const f=Le(this.layout.layoutBox.x);a.x.min=o.target.x.min,a.x.max=a.x.min+f;const d=Le(this.layout.layoutBox.y);a.y.min=o.target.y.min,a.y.max=a.y.min+d}_e(l,a),Un(l,c),Hr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new HT),this.sharedNodes.get(o).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:l}=this.options;return l?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:l}=this.options;return l?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:a}=o;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&ul("z",o,u,this.animationValues);for(let c=0;c<al.length;c++)ul(`rotate${al[c]}`,o,u,this.animationValues),ul(`skew${al[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return KT;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=fs(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=fs(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!nn(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=bT(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:g,y}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${y.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const x in $s){if(d[x]===void 0)continue;const{correct:T,applyTo:p}=$s[x],h=u.transform==="none"?d[x]:T(d[x],f);if(p){const m=p.length;for(let w=0;w<m;w++)u[p[w]]=h}else u[x]=h}return this.options.layoutId&&(u.pointerEvents=f===this?fs(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Nd),this.root.sharedNodes.clear()}}}function QT(e){e.updateLayout()}function YT(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Ve(f=>{const d=o?n.measuredBox[f]:n.layoutBox[f],g=Le(d);d.min=r[f].min,d.max=d.min+g}):Yg(s,n.layoutBox,r)&&Ve(f=>{const d=o?n.measuredBox[f]:n.layoutBox[f],g=Le(r[f]);d.max=d.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+g)});const l=zn();Hr(l,r,n.layoutBox);const a=zn();o?Hr(a,e.applyTransform(i,!0),n.measuredBox):Hr(a,r,n.layoutBox);const u=!bg(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:g}=f;if(d&&g){const y=Z();br(y,n.layoutBox,d.layoutBox);const x=Z();br(x,r,g.layoutBox),Kg(y,x)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=y,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function XT(e){Dr&&rn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ZT(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function qT(e){e.clearSnapshot()}function Nd(e){e.clearMeasurements()}function JT(e){e.isLayoutDirty=!1}function eC(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function _d(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function tC(e){e.resolveTargetDelta()}function nC(e){e.calcProjection()}function rC(e){e.resetSkewAndRotation()}function iC(e){e.removeLeadSnapshot()}function Vd(e,t,n){e.translate=H(t.translate,0,n),e.scale=H(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Od(e,t,n,r){e.min=H(t.min,n.min,r),e.max=H(t.max,n.max,r)}function sC(e,t,n,r){Od(e.x,t.x,n.x,r),Od(e.y,t.y,n.y,r)}function oC(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const lC={duration:.45,ease:[.4,0,.1,1]},Id=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Fd=Id("applewebkit/")&&!Id("chrome/")?Math.round:je;function zd(e){e.min=Fd(e.min),e.max=Fd(e.max)}function aC(e){zd(e.x),zd(e.y)}function Yg(e,t,n){return e==="position"||e==="preserve-aspect"&&!fT(Md(t),Md(n),.2)}function uC(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const cC=Qg({attachResizeListener:(e,t)=>mi(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),cl={current:void 0},Xg=Qg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!cl.current){const e=new cC({});e.mount(window),e.setOptions({layoutScroll:!0}),cl.current=e}return cl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),fC={pan:{Feature:AT},drag:{Feature:ET,ProjectionNode:Xg,MeasureLayout:$g}};function Bd(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,s=r[i];s&&U.postRender(()=>s(t,ki(t)))}class dC extends Qt{mount(){const{current:t}=this.node;t&&(this.unmount=fw(t,n=>(Bd(this.node,n,"Start"),r=>Bd(this.node,r,"End"))))}unmount(){}}class hC extends Qt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Pi(mi(this.node.current,"focus",()=>this.onFocus()),mi(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ud(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),s=r[i];s&&U.postRender(()=>s(t,ki(t)))}class pC extends Qt{mount(){const{current:t}=this.node;t&&(this.unmount=mw(t,n=>(Ud(this.node,n,"Start"),(r,{success:i})=>Ud(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ja=new WeakMap,fl=new WeakMap,mC=e=>{const t=ja.get(e.target);t&&t(e)},gC=e=>{e.forEach(mC)};function yC({root:e,...t}){const n=e||document;fl.has(n)||fl.set(n,{});const r=fl.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(gC,{root:e,...t})),r[i]}function vC(e,t,n){const r=yC(t);return ja.set(e,n),r.observe(e),()=>{ja.delete(e),r.unobserve(e)}}const xC={some:0,all:1};class wC extends Qt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:xC[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return vC(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(SC(t,n))&&this.startObserver()}unmount(){}}function SC({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const TC={inView:{Feature:wC},tap:{Feature:pC},focus:{Feature:hC},hover:{Feature:dC}},CC={layout:{ProjectionNode:Xg,MeasureLayout:$g}},Qs={current:null},sc={current:!1};function Zg(){if(sc.current=!0,!!Ru)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Qs.current=e.matches;e.addListener(t),t()}else Qs.current=!1}const PC=[...Tg,he,Wt],kC=e=>PC.find(Sg(e)),$d=new WeakMap;function EC(e,t,n){for(const r in t){const i=t[r],s=n[r];if(me(i))e.addValue(r,i);else if(me(s))e.addValue(r,hi(i,{owner:e}));else if(s!==i)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(r);e.addValue(r,hi(o!==void 0?o:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Wd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class AC{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=st.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,U.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:c}=o;this.onUpdate=c,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!s,this.isControllingVariants=xo(n),this.isVariantNode=Mm(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:f,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in d){const y=d[g];a[g]!==void 0&&me(y)&&y.set(a[g],!1)}}mount(t){this.current=t,$d.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),sc.current||Zg(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Qs.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){$d.delete(this.current),this.projection&&this.projection.unmount(),$t(this.notifyUpdate),$t(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=Tn.has(t),i=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&U.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in sr){const n=sr[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Z()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Wd.length;r++){const i=Wd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s="on"+i,o=t[s];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=EC(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=hi(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(xg(i)||cg(i))?i=parseFloat(i):!kC(i)&&Wt.test(n)&&(i=gg(t,n)),this.setBaseTarget(t,me(i)?i.get():i)),me(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const o=Vu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[t])}if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!me(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Yu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class qg extends AC{constructor(){super(...arguments),this.KeyframeResolver=Cg}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;me(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function RC(e){return window.getComputedStyle(e)}class jC extends qg{constructor(){super(...arguments),this.type="html",this.renderInstance=zm}readValueFromInstance(t,n){if(Tn.has(n)){const r=ec(n);return r&&r.default||0}else{const r=RC(t),i=(Om(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Bg(t,n)}build(t,n,r){Fu(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return $u(t,n,r)}}class MC extends qg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Z}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Tn.has(n)){const r=ec(n);return r&&r.default||0}return n=Bm.has(n)?n:Du(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Wm(t,n,r)}build(t,n,r){zu(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){Um(t,n,r,i)}mount(t){this.isSVGTag=Uu(t.tagName),super.mount(t)}}const LC=(e,t)=>_u(e)?new MC(t):new jC(t,{allowProjection:e!==S.Fragment}),DC=iw({...nT,...TC,...fC,...CC},LC),At=x1(DC);function NC(){!sc.current&&Zg();const[e]=S.useState(Qs.current);return e}function Hd(){const[e,t]=S.useState(()=>{if(typeof window>"u")return!1;const n=localStorage.getItem("theme");return n?n==="dark":window.matchMedia("(prefers-color-scheme: dark)").matches});return S.useEffect(()=>{const n=document.documentElement;e?(n.classList.add("dark"),localStorage.setItem("theme","dark")):(n.classList.remove("dark"),localStorage.setItem("theme","light"))},[e]),v.jsx("button",{className:"btn btn-ghost",onClick:()=>t(n=>!n),"aria-pressed":e,"aria-label":"Toggle dark mode",children:e?"🌙":"☀️"})}function _C(){const[e,t]=S.useState(!1);S.useEffect(()=>{const r=i=>i.key==="Escape"&&t(!1);return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[]);const n=({to:r,children:i})=>v.jsx(jx,{to:r,onClick:()=>t(!1),className:({isActive:s})=>`px-3 py-2 rounded hover:bg-neutral-100 dark:hover:bg-neutral-900 ${s?"font-semibold text-brand-600 dark:text-brand-400":""}`,children:i});return v.jsxs("header",{className:"sticky top-0 z-40 backdrop-blur bg-white/60 dark:bg-black/40 border-b border-neutral-200/60 dark:border-neutral-800",children:[v.jsxs("nav",{className:"container-px max-w-7xl mx-auto flex items-center justify-between h-16",children:[v.jsxs(Bs,{to:"/",className:"font-extrabold tracking-tight text-lg",children:["Ragul",v.jsx("span",{className:"text-brand-500",children:"."})]}),v.jsxs("div",{className:"hidden md:flex items-center gap-1",children:[v.jsx(n,{to:"/",children:"Home"}),v.jsx(n,{to:"/about",children:"About"}),v.jsx(n,{to:"/projects",children:"Projects"}),v.jsx(n,{to:"/blog",children:"Blog"}),v.jsx(n,{to:"/contact",children:"Contact"}),v.jsx(n,{to:"/admin",children:"Admin"}),v.jsx(Hd,{})]}),v.jsx("button",{"aria-label":"Menu",className:"md:hidden btn btn-ghost",onClick:()=>t(!e),"aria-expanded":e,children:"☰"})]}),e&&v.jsx("div",{className:"md:hidden border-t border-neutral-200 dark:border-neutral-800 container-px pb-3",children:v.jsxs("div",{className:"flex flex-col gap-1 py-2",children:[v.jsx(n,{to:"/",children:"Home"}),v.jsx(n,{to:"/about",children:"About"}),v.jsx(n,{to:"/projects",children:"Projects"}),v.jsx(n,{to:"/blog",children:"Blog"}),v.jsx(n,{to:"/contact",children:"Contact"}),v.jsx(n,{to:"/admin",children:"Admin"}),v.jsx(Hd,{})]})})]})}function VC(){return v.jsx("footer",{className:"mt-10 border-t border-neutral-200 dark:border-neutral-800",children:v.jsxs("div",{className:"container-px max-w-7xl mx-auto py-8 text-sm flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[v.jsxs("p",{children:["© ",new Date().getFullYear()," Ragul. All rights reserved."]}),v.jsxs("div",{className:"flex items-center gap-4",children:[v.jsx("a",{href:"/privacy",className:"hover:underline",children:"Privacy"}),v.jsx("a",{href:"https://github.com/",target:"_blank",rel:"noreferrer","aria-label":"GitHub",className:"hover:text-brand-500",children:"GitHub"}),v.jsx("a",{href:"https://linkedin.com/",target:"_blank",rel:"noreferrer","aria-label":"LinkedIn",className:"hover:text-brand-500",children:"LinkedIn"}),v.jsx("a",{href:"/resume.pdf",className:"hover:text-brand-500",children:"Resume"})]})]})})}const dl=["Indexing projects…","Warming the neurons…","Securing keys…","Optimizing pixels…","Spinning up ideas…"];function OC(){const e=NC(),[t,n]=S.useState(0),[r,i]=S.useState(!1);return S.useEffect(()=>{const s=setInterval(()=>n(l=>(l+1)%dl.length),700),o=setTimeout(()=>i(!0),6e3);return()=>{clearInterval(s),clearTimeout(o)}},[]),r?null:v.jsx("div",{role:"status","aria-live":"polite",className:"min-h-dvh grid place-items-center bg-gradient-to-b from-neutral-50 to-white dark:from-black dark:to-neutral-950",children:e?v.jsxs("div",{className:"text-center",children:[v.jsx("div",{className:"w-64 h-2 bg-neutral-200 dark:bg-neutral-800 rounded-full overflow-hidden",children:v.jsx("div",{className:"h-full w-1/2 bg-brand-500 animate-pulse rounded"})}),v.jsx("p",{className:"mt-4 text-sm text-neutral-600 dark:text-neutral-400",children:dl[t]})]}):v.jsx(IC,{status:dl[t]})})}function IC({status:e}){const t=S.useMemo(()=>({fingerprint:"M50,10 C75,10 90,30 90,55 C90,80 70,95 50,95 C30,95 10,80 10,55 C10,30 25,10 50,10 Z",rshape:"M20,20 L55,20 C70,20 80,30 80,45 C80,60 70,70 55,70 L40,70 L70,95"}),[]);return v.jsxs("div",{className:"flex flex-col items-center",children:[v.jsxs(At.svg,{width:"240",height:"240",viewBox:"0 0 100 110","aria-hidden":"true",children:[v.jsx("defs",{children:v.jsxs("radialGradient",{id:"g",cx:"50%",cy:"50%",r:"60%",children:[v.jsx("stop",{offset:"0%",stopColor:"#6C5CE7"}),v.jsx("stop",{offset:"100%",stopColor:"#3D3488"})]})}),v.jsx(At.path,{d:t.fingerprint,fill:"none",stroke:"url(#g)",strokeWidth:"3",initial:{pathLength:0,opacity:1},animate:{pathLength:1,opacity:0},transition:{duration:2.2,ease:"easeInOut"}}),v.jsx(At.path,{d:t.rshape,fill:"none",stroke:"url(#g)",strokeWidth:"6",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:1},transition:{duration:2.6,ease:"easeInOut",delay:1.2}}),v.jsx(At.circle,{r:"2.2",fill:"#6C5CE7",initial:{pathLength:0,offsetDistance:"0%"},animate:{offsetDistance:"100%"},transition:{duration:2,repeat:1/0,repeatType:"reverse"},style:{offsetPath:`path('${t.rshape}')`}})]}),v.jsx(At.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 text-sm text-neutral-600 dark:text-neutral-400",children:e})]})}function FC(){return v.jsxs("section",{className:"py-12 sm:py-16",children:[v.jsxs(Sn,{children:[v.jsx("title",{children:"Ragul — Web & App Developer"}),v.jsx("meta",{name:"description",content:"Portfolio of Ragul, a web & mobile developer crafting fast, accessible experiences."}),v.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"Person",name:"Ragul",url:"https://ragul.dev",jobTitle:"Web & App Developer"})})]}),v.jsxs("div",{className:"grid gap-10 lg:grid-cols-2 items-center",children:[v.jsxs("div",{children:[v.jsx(At.h1,{initial:{opacity:0,y:12},animate:{opacity:1,y:0},transition:{duration:.4},className:"text-4xl sm:text-5xl font-extrabold tracking-tight",children:"Hi, I’m Ragul — I build crisp, performant web & mobile apps"}),v.jsx("p",{className:"mt-4 text-neutral-600 dark:text-neutral-300 max-w-prose",children:"From idea to production, I ship delightful experiences with React, TypeScript, Node, and a sprinkle of motion."}),v.jsxs("div",{className:"mt-6 flex flex-wrap gap-3",children:[v.jsx(Bs,{to:"/projects",className:"btn btn-primary",children:"View Projects"}),v.jsx(Bs,{to:"/contact",className:"btn btn-ghost",children:"Contact Me"})]}),v.jsx("p",{className:"mt-3 text-xs text-neutral-500",children:"Tanglish: namma appu build pannalaama? 😄"})]}),v.jsxs(At.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"card p-6",children:[v.jsx("h2",{className:"font-semibold",children:"Quick stats"}),v.jsxs("ul",{className:"mt-3 grid grid-cols-2 gap-3 text-sm",children:[v.jsxs("li",{children:[v.jsx("span",{className:"text-2xl font-extrabold",children:"25+"}),v.jsx("br",{}),"Projects shipped"]}),v.jsxs("li",{children:[v.jsx("span",{className:"text-2xl font-extrabold",children:"90+"}),v.jsx("br",{}),"Lighthouse perf"]}),v.jsxs("li",{children:[v.jsx("span",{className:"text-2xl font-extrabold",children:"5"}),v.jsx("br",{}),"Years building"]}),v.jsxs("li",{children:[v.jsx("span",{className:"text-2xl font-extrabold",children:"∞"}),v.jsx("br",{}),"Curiosity"]})]})]})]})]})}function zC(){return v.jsxs("section",{className:"py-12",children:[v.jsx(Sn,{children:v.jsx("title",{children:"About — Ragul"})}),v.jsx("h1",{className:"text-3xl font-bold",children:"About"}),v.jsx("p",{className:"mt-4 max-w-prose text-neutral-700 dark:text-neutral-300",children:"I’m Ragul. I help teams design, build, and ship high-quality web & mobile products. I care about performance, accessibility, and clean DX. In my free time, I experiment with micro-interactions and learning Tamil typography."}),v.jsx("h2",{className:"mt-8 font-semibold",children:"Skills"}),v.jsxs("ul",{className:"mt-3 grid sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm",children:[v.jsx("li",{className:"card p-4",children:"React, React Native, TypeScript"}),v.jsx("li",{className:"card p-4",children:"Node/Express, MongoDB"}),v.jsx("li",{className:"card p-4",children:"Framer Motion, Tailwind"}),v.jsx("li",{className:"card p-4",children:"Testing Library, Playwright"}),v.jsx("li",{className:"card p-4",children:"CI/CD, Vercel, Render"}),v.jsx("li",{className:"card p-4",children:"UX Research, Accessibility"})]})]})}const bd=[{name:"GreenMate — Habit Builder",slug:"greenmate",summary:"Tiny web app to build eco-habits with streaks and playful visuals.",tech:["React","TypeScript","Vite"],thumbnail:"/images/greenmate.jpg",demo:"https://greenmate-demo.vercel.app",repo:"https://github.com/ragul/greenmate"},{name:"Biometric Login Prototype",slug:"biometric-login",summary:"A secure prototype integrating WebAuthn and a fingerprint-style onboarding.",tech:["React","Framer Motion","WebAuthn"],thumbnail:"/images/biometric.jpg",demo:"https://biometric-demo.vercel.app",repo:"https://github.com/ragul/biometric-demo"},{name:"Portfolio v3",slug:"portfolio-v3",summary:"Modern, accessible portfolio with blog, MDX, and serverless contact form.",tech:["React","Tailwind","Vercel"],thumbnail:"/images/portfolio.jpg",demo:"https://ragul.dev",repo:"https://github.com/ragul/portfolio-v3"}];function BC(){const[e,t]=S.useState(""),[n,r]=S.useState("All"),i=S.useMemo(()=>["All",...Array.from(new Set(bd.flatMap(o=>o.tech)))],[]),s=S.useMemo(()=>bd.filter(o=>(n==="All"||o.tech.includes(n))&&(o.name.toLowerCase().includes(e.toLowerCase())||o.summary.toLowerCase().includes(e.toLowerCase()))),[e,n]);return v.jsxs("section",{className:"py-12",children:[v.jsx(Sn,{children:v.jsx("title",{children:"Projects — Ragul"})}),v.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3 justify-between",children:[v.jsx("h1",{className:"text-3xl font-bold",children:"Projects"}),v.jsxs("div",{className:"flex gap-2 items-center",children:[v.jsx("input",{value:e,onChange:o=>t(o.target.value),placeholder:"Search projects",className:"px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent"}),v.jsx("select",{value:n,onChange:o=>r(o.target.value),className:"px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent",children:i.map(o=>v.jsx("option",{value:o,children:o},o))})]})]}),v.jsx("ul",{className:"mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-5",children:s.map(o=>v.jsxs("li",{className:"card p-4",children:[v.jsx("img",{src:o.thumbnail,alt:"",className:"rounded-lg aspect-video object-cover",loading:"lazy"}),v.jsx("h3",{className:"mt-3 font-semibold",children:o.name}),v.jsx("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:o.summary}),v.jsx("div",{className:"mt-3 flex flex-wrap gap-1",children:o.tech.map(l=>v.jsx("span",{className:"text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-900",children:l},l))}),v.jsxs("div",{className:"mt-4 flex gap-2",children:[v.jsx("a",{href:o.demo,target:"_blank",rel:"noreferrer",className:"btn btn-primary",children:"Live"}),v.jsx("a",{href:o.repo,target:"_blank",rel:"noreferrer",className:"btn btn-ghost",children:"Code"})]})]},o.slug))})]})}const UC=[{title:"Animating with intent",slug:"animating-with-intent",excerpt:"How to use motion to clarify, not distract.",date:"2024-11-12",tags:["UX","Motion"]},{title:"Ship faster with Vite + Tailwind",slug:"vite-tailwind",excerpt:"My go-to stack for rapid product spikes.",date:"2024-08-04",tags:["Vite","Tailwind"]},{title:"Privacy-first analytics",slug:"privacy-first-analytics",excerpt:"Why I use Plausible over heavy trackers.",date:"2023-05-22",tags:["Privacy","Analytics"]}];function $C(){return v.jsxs("section",{className:"py-12",children:[v.jsx(Sn,{children:v.jsx("title",{children:"Blog — Ragul"})}),v.jsx("h1",{className:"text-3xl font-bold",children:"Blog / Notes"}),v.jsx("ul",{className:"mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-5",children:UC.map(e=>v.jsxs("li",{className:"card p-4",children:[v.jsx("h3",{className:"font-semibold",children:e.title}),v.jsx("p",{className:"text-sm text-neutral-600 dark:text-neutral-400",children:e.excerpt}),v.jsxs("div",{className:"mt-3 flex flex-wrap gap-1 text-xs text-neutral-500",children:[v.jsx("span",{children:new Date(e.date).toLocaleDateString()}),v.jsx("span",{children:"•"}),v.jsx("span",{children:e.tags.join(", ")})]})]},e.slug))})]})}function WC(){const[e,t]=S.useState("idle"),[n,r]=S.useState("");async function i(s){s.preventDefault(),t("loading");const o=new FormData(s.currentTarget);if(o.get("company"))return t("success");try{const l=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:o.get("name"),email:o.get("email"),message:o.get("message")})}),a=await l.json();if(!l.ok)throw new Error(a.error||"Failed");t("success"),r("Thanks! I will get back soon."),s.target.reset()}catch(l){t("error"),r(l.message)}}return v.jsxs("section",{className:"py-12",children:[v.jsx(Sn,{children:v.jsx("title",{children:"Contact — Ragul"})}),v.jsx("h1",{className:"text-3xl font-bold",children:"Contact"}),v.jsxs("form",{className:"mt-6 max-w-xl card p-6",onSubmit:i,children:[v.jsxs("label",{className:"block",children:["Name",v.jsx("input",{name:"name",required:!0,className:"mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent"})]}),v.jsxs("label",{className:"block mt-4",children:["Email",v.jsx("input",{type:"email",name:"email",required:!0,className:"mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent"})]}),v.jsxs("label",{className:"block mt-4",children:["Message",v.jsx("textarea",{name:"message",required:!0,rows:5,className:"mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent"})]}),v.jsx("input",{type:"text",name:"company",tabIndex:-1,autoComplete:"off",className:"hidden"}),v.jsx("button",{disabled:e==="loading",className:"btn btn-primary mt-6",children:e==="loading"?"Sending…":"Send"}),n&&v.jsx("p",{className:"mt-3 text-sm",children:n})]})]})}function HC(){return v.jsxs("section",{className:"py-12",children:[v.jsx(Sn,{children:v.jsx("title",{children:"Admin — Ragul"})}),v.jsx("h1",{className:"text-3xl font-bold",children:"Admin CMS (Stub)"}),v.jsx("p",{className:"mt-4 max-w-prose text-neutral-700 dark:text-neutral-300",children:"This is a stub for managing projects and blog posts. Hook this to a simple Express API with MongoDB and add authentication (e.g., passwordless magic link or GitHub OAuth). For now, data is read from static JSON files."}),v.jsxs("ul",{className:"mt-6 list-disc pl-6 text-sm",children:[v.jsx("li",{children:"Create / Edit Project"}),v.jsx("li",{children:"Create / Edit Blog Post (MDX)"}),v.jsx("li",{children:"Draft / Publish flags"})]})]})}function bC(){const e=fr(),[t,n]=S.useState(!1);return S.useEffect(()=>{const r=setTimeout(()=>n(!0),2e3);return()=>clearTimeout(r)},[]),v.jsxs("div",{className:"min-h-dvh flex flex-col",children:[v.jsxs(Sn,{children:[v.jsx("html",{lang:"en"}),v.jsx("meta",{name:"robots",content:"index,follow"})]}),t?v.jsxs(v.Fragment,{children:[v.jsx("a",{href:"#main",className:"sr-only focus:not-sr-only focus:fixed focus:top-2 focus:left-2 bg-black text-white px-3 py-2 rounded",children:"Skip to content"}),v.jsx(_C,{}),v.jsx("main",{id:"main",className:"flex-1 container-px max-w-7xl mx-auto w-full",children:v.jsx(f1,{mode:"wait",initial:!1,children:v.jsx(At.div,{initial:{opacity:0,y:8},animate:{opacity:1,y:0},exit:{opacity:0,y:-8},transition:{duration:.25},children:v.jsx(S.Suspense,{fallback:v.jsx("div",{className:"p-6",children:"Loading…"}),children:v.jsxs(vx,{children:[v.jsx(tn,{path:"/",element:v.jsx(FC,{})}),v.jsx(tn,{path:"/about",element:v.jsx(zC,{})}),v.jsx(tn,{path:"/projects",element:v.jsx(BC,{})}),v.jsx(tn,{path:"/blog",element:v.jsx($C,{})}),v.jsx(tn,{path:"/contact",element:v.jsx(WC,{})}),v.jsx(tn,{path:"/admin",element:v.jsx(HC,{})})]})})},e.pathname)})}),v.jsx(VC,{})]}):v.jsx(OC,{})]})}pl.createRoot(document.getElementById("root")).render(v.jsx(tt.StrictMode,{children:v.jsx(Cm,{children:v.jsx(Ex,{children:v.jsx(bC,{})})})}));
