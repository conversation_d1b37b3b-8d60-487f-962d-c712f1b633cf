# Portfolio Loader Setup Guide

## Installation & Dependencies

```bash
# Install Framer Motion (already in package.json)
npm install framer-motion

# Tailwind + PostCSS already configured
# Vite already configured with React
```

## Component Structure

```
src/components/
├── LoadingScreen.tsx      # Main PortfolioLoader component
├── ParticleBackground.tsx # Reusable animated particle canvas
└── ...other components
```

## Usage Example

```tsx
// App.tsx
import { useState } from "react";
import PortfolioLoader from "./components/LoadingScreen";

export default function App() {
  const [booted, setBooted] = useState(false);

  return (
    <div className="min-h-screen bg-black text-white">
      {!booted && (
        <PortfolioLoader
          duration={5000}            // optional, default 5000ms
          onFinish={() => setBooted(true)}
        />
      )}
      {/* Main site content fades in under the loader */}
      <div className={`transition-opacity duration-700 ${booted ? "opacity-100" : "opacity-0"}`}>
        <header className="p-8">Your Portfolio Content</header>
        <main className="p-8">
          <h1 className="text-3xl font-bold">Welcome!</h1>
          <p className="text-neutral-300 mt-2">Content appears after loader.</p>
        </main>
      </div>
    </div>
  );
}
```

## Features Included

### 1. Centerpiece Animation
- **Morphing SVG**: Fingerprint → "R" initial → Rocket
- **Framer Motion**: Uses `pathLength`, `opacity`, and `scale`
- **Staggered timing**: Each shape appears/disappears at different intervals
- **Glassy shine**: Moving highlight overlay with `mix-blend-mode: screen`

### 2. Background Animations
- **Animated gradient**: CSS custom properties with hue rotation
- **Particle system**: Canvas-based with 28 particles (performance optimized)
- **Mouse interaction**: Particles drift gently toward cursor
- **Breathing effect**: Particles pulse brightness using sine waves

### 3. Dynamic Text
- **Typewriter effect**: Characters appear one by one (28ms intervals)
- **Message cycling**: 6 witty messages rotate every 700-900ms
- **ARIA live region**: Screen reader accessible

### 4. Progress Indicator
- **Glowing bar**: Bottom progress bar with shimmer animation
- **Color sync**: Hue shifts match background gradient
- **Real-time**: Tied to animation progress (0-100%)

### 5. Accessibility & Performance
- **Reduced motion**: Static fallback for `prefers-reduced-motion`
- **Limited particles**: 28 particles max for smooth FPS
- **Semantic HTML**: Proper ARIA labels and roles
- **Canvas optimization**: Device pixel ratio clamped to 2

### 6. Smooth Transitions
- **Fade out**: Opacity + scale animation when complete
- **Callback**: `onFinish` prop triggers main site reveal
- **Duration control**: Configurable timing (default 5s)

## Performance Optimization Notes

```txt
✅ Particle count limited to 28 for smooth 60fps
✅ Canvas uses devicePixelRatio clamped to max 2
✅ requestAnimationFrame loops cleaned up on unmount
✅ Gentle motion (low velocity, damped) reduces overdraw
✅ prefers-reduced-motion shows static logo only
✅ Path morphing uses crossfade illusion vs expensive morphing
✅ Gradient animations use CSS filters (GPU-friendly)
✅ Mouse tracking uses passive event listeners
```

## Customization

### Change Messages
```tsx
const CUSTOM_MESSAGES = [
  "Loading your creativity...",
  "Brewing fresh ideas...",
  "Calibrating awesome...",
];
```

### Adjust Particle Count
```tsx
<ParticleBackground count={20} progress={progress} fadeOut={done} />
```

### Modify Duration
```tsx
<PortfolioLoader duration={3000} onFinish={handleFinish} />
```

### Custom Colors
Update the hue transform range in LoadingScreen.tsx:
```tsx
const hue = useTransform(p, [0, 1], [180, 280]); // blue → purple
```
