/* web-background.css */
/* FIXED: Additional CSS utilities for full-screen web background */
/* Ensures consistent behavior across all browsers and devices */

/* Full-screen web background container */
.web-background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

/* Canvas specific styles */
.web-background-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
}

/* Prevent layout shifts */
.web-background-no-shift {
  contain: layout style paint;
  will-change: auto;
}

/* Performance optimizations */
.web-background-optimized {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Responsive opacity utilities */
.web-bg-opacity-loading {
  opacity: 0.6;
}

.web-bg-opacity-home {
  opacity: 0.4;
}

.web-bg-opacity-page {
  opacity: 0.2;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  .web-background-container {
    transform: translate3d(0, 0, 0);
  }
  
  .web-bg-opacity-loading {
    opacity: 0.5;
  }
  
  .web-bg-opacity-home {
    opacity: 0.3;
  }
  
  .web-bg-opacity-page {
    opacity: 0.15;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  .web-bg-opacity-loading {
    opacity: 0.55;
  }
  
  .web-bg-opacity-home {
    opacity: 0.35;
  }
  
  .web-bg-opacity-page {
    opacity: 0.18;
  }
}

/* Reduced motion fallback */
@media (prefers-reduced-motion: reduce) {
  .web-background-container {
    animation: none;
    transition: none;
  }
  
  .web-background-canvas {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .web-bg-opacity-loading {
    opacity: 0.3;
  }
  
  .web-bg-opacity-home {
    opacity: 0.2;
  }
  
  .web-bg-opacity-page {
    opacity: 0.1;
  }
}

/* Print styles - hide background */
@media print {
  .web-background-container {
    display: none !important;
  }
}
