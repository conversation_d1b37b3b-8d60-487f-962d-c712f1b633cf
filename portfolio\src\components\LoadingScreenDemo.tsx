// Demo component to test the optimized loading screen
import { useState } from "react";
import PortfolioLoader from "./LoadingScreen";

export default function LoadingScreenDemo() {
  const [showLoader, setShowLoader] = useState(true);

  const handleFinish = () => {
    setShowLoader(false);
    console.log("Loading finished!");
  };

  const resetDemo = () => {
    setShowLoader(true);
  };

  if (showLoader) {
    return (
      <PortfolioLoader 
        onFinish={handleFinish} 
        duration={3000} 
        allowSkip={true} 
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          Loading Complete!
        </h1>
        <p className="text-gray-600 mb-6">
          The optimized loading screen has finished.
        </p>
        <button
          onClick={resetDemo}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Test Again
        </button>
      </div>
    </div>
  );
}
