// WebBackgroundTest.tsx
// FIXED: Test component to verify full-screen web background functionality
// Tests: viewport coverage, responsive behavior, performance optimization

import { useState, useEffect } from "react";
import FullScreenWebBackground from "./FullScreenWebBackground";

export default function WebBackgroundTest() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    isTablet: false,
    pixelRatio: 1
  });

  useEffect(() => {
    const updateInfo = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
      
      setDeviceInfo({
        isMobile: window.innerWidth < 768,
        isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
        pixelRatio: window.devicePixelRatio || 1
      });
    };

    updateInfo();
    window.addEventListener('resize', updateInfo);
    return () => window.removeEventListener('resize', updateInfo);
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 text-white relative">
      {/* FIXED: Full-screen web background */}
      <FullScreenWebBackground 
        variant="home"
        animationSpeed={1.0}
        density="medium"
        colorTheme="blue"
        enableCursorInteraction={true}
        enableParticles={true}
        opacity={50}
      />

      {/* Test overlay to verify full coverage */}
      <div className="relative z-10 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-8 text-center">
            🕷️ Full-Screen Web Background Test
          </h1>

          {/* Viewport Info */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Viewport Info</h2>
              <div className="space-y-2 text-sm">
                <div>Width: <span className="text-blue-400">{dimensions.width}px</span></div>
                <div>Height: <span className="text-blue-400">{dimensions.height}px</span></div>
                <div>Aspect Ratio: <span className="text-blue-400">{(dimensions.width / dimensions.height).toFixed(2)}</span></div>
                <div>Pixel Ratio: <span className="text-blue-400">{deviceInfo.pixelRatio}x</span></div>
              </div>
            </div>

            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Device Detection</h2>
              <div className="space-y-2 text-sm">
                <div>Mobile: <span className={deviceInfo.isMobile ? "text-green-400" : "text-red-400"}>
                  {deviceInfo.isMobile ? "Yes" : "No"}
                </span></div>
                <div>Tablet: <span className={deviceInfo.isTablet ? "text-green-400" : "text-red-400"}>
                  {deviceInfo.isTablet ? "Yes" : "No"}
                </span></div>
                <div>Desktop: <span className={!deviceInfo.isMobile && !deviceInfo.isTablet ? "text-green-400" : "text-red-400"}>
                  {!deviceInfo.isMobile && !deviceInfo.isTablet ? "Yes" : "No"}
                </span></div>
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">✅ Test Checklist</h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h3 className="font-semibold text-blue-400 mb-2">Visual Tests:</h3>
                <ul className="space-y-1">
                  <li>• Web pattern covers entire viewport</li>
                  <li>• No empty spaces or cropping</li>
                  <li>• Smooth animations at 30-60fps</li>
                  <li>• Proper opacity and layering</li>
                  <li>• Web nodes react to mouse movement</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-blue-400 mb-2">Responsive Tests:</h3>
                <ul className="space-y-1">
                  <li>• Resize browser window</li>
                  <li>• Test mobile/tablet/desktop</li>
                  <li>• Check different aspect ratios</li>
                  <li>• Verify performance on mobile</li>
                  <li>• Test reduced motion preference</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Corner Markers */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">🎯 Viewport Coverage Test</h2>
            <p className="text-sm text-gray-300 mb-4">
              The colored markers below should be at the exact corners of your viewport.
              If you see any gaps or the web pattern doesn't reach the edges, there's a positioning issue.
            </p>
            
            {/* Corner markers to verify full coverage */}
            <div className="fixed top-0 left-0 w-4 h-4 bg-red-500 z-50 opacity-75"></div>
            <div className="fixed top-0 right-0 w-4 h-4 bg-green-500 z-50 opacity-75"></div>
            <div className="fixed bottom-0 left-0 w-4 h-4 bg-blue-500 z-50 opacity-75"></div>
            <div className="fixed bottom-0 right-0 w-4 h-4 bg-yellow-500 z-50 opacity-75"></div>
            
            <div className="text-xs text-gray-400">
              Red (top-left) • Green (top-right) • Blue (bottom-left) • Yellow (bottom-right)
            </div>
          </div>

          {/* Performance Info */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">⚡ Performance Features</h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h3 className="font-semibold text-green-400 mb-2">Optimizations:</h3>
                <ul className="space-y-1 text-gray-300">
                  <li>• Adaptive particle count</li>
                  <li>• Frame rate throttling</li>
                  <li>• Debounced resize handling</li>
                  <li>• Lazy loading support</li>
                  <li>• Memory-efficient cleanup</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-green-400 mb-2">Accessibility:</h3>
                <ul className="space-y-1 text-gray-300">
                  <li>• Reduced motion fallback</li>
                  <li>• Static background option</li>
                  <li>• Proper z-index layering</li>
                  <li>• No layout interference</li>
                  <li>• Pointer events disabled</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Test Content */}
          <div className="mt-12 space-y-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
              <h2 className="text-2xl font-bold mb-4">Sample Content</h2>
              <p className="text-gray-300 mb-4">
                This content should be clearly readable over the web background.
                The background should not interfere with text readability or user interactions.
              </p>
              <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                Test Button
              </button>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="bg-white/5 backdrop-blur-sm rounded-lg p-6">
                  <h3 className="font-semibold mb-2">Card {i}</h3>
                  <p className="text-sm text-gray-400">
                    Test card to verify background doesn't interfere with content layout.
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-12 text-center text-sm text-gray-400">
            <p>Move your mouse around to test cursor interaction.</p>
            <p>Resize the browser window to test responsive behavior.</p>
            <p>Check browser dev tools for performance metrics.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
