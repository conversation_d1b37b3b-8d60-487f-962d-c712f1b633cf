import { Helmet } from 'react-helmet-async';

export default function Privacy() {
  return (
    <section className="py-12">
      <Helmet><title>Privacy — Ragul</title></Helmet>
      <h1 className="text-3xl font-bold">Privacy</h1>
      <p className="mt-4 max-w-prose text-neutral-700 dark:text-neutral-300">
        I keep analytics minimal and privacy-friendly. If enabled, I use Plausible to measure page views without cookies. Contact form submissions are stored securely for follow-up and never sold. For data requests, email me.
      </p>
    </section>
  );
}

