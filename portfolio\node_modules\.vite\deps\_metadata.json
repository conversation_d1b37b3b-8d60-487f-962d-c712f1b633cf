{"hash": "999382d9", "configHash": "0aa7f635", "lockfileHash": "6561edf9", "browserHash": "37d01648", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ae7c8b92", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b6302455", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "31027f98", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2e68b0a9", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "aa29f322", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0093451c", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "ae6110e8", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "52b6ef65", "needsInterop": false}}, "chunks": {"chunk-OOIH53S6": {"file": "chunk-OOIH53S6.js"}, "chunk-NTJKIE7D": {"file": "chunk-NTJKIE7D.js"}, "chunk-WXTH2UMW": {"file": "chunk-WXTH2UMW.js"}}}