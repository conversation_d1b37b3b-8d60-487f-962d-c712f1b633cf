import { Helmet } from 'react-helmet-async';
import FullScreenWebBackground, { getResponsiveOpacity, getResponsiveAnimationSpeed } from '../components/FullScreenWebBackground';

export default function About() {
  return (
    <>
      {/* FIXED: Full-screen subtle web background for content pages */}
      <FullScreenWebBackground
        variant="page"
        animationSpeed={getResponsiveAnimationSpeed(0.5)}
        density="low"
        colorTheme="auto"
        enableCursorInteraction={true}
        enableParticles={false}
        opacity={getResponsiveOpacity('page')}
      />

      <section className="relative py-12">
      <Helmet>
        <title>About — Ragul</title>
      </Helmet>
      <h1 className="text-3xl font-bold">About</h1>
      <p className="mt-4 max-w-prose text-neutral-700 dark:text-neutral-300">I’m Ragul. I help teams design, build, and ship high-quality web & mobile products. I care about performance, accessibility, and clean DX. In my free time, I experiment with micro-interactions and learning Tamil typography.</p>
      <h2 className="mt-8 font-semibold">Skills</h2>
      <ul className="mt-3 grid sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
        <li className="card p-4">React, React Native, TypeScript</li>
        <li className="card p-4">Node/Express, MongoDB</li>
        <li className="card p-4">Framer Motion, Tailwind</li>
        <li className="card p-4">Testing Library, Playwright</li>
        <li className="card p-4">CI/CD, Vercel, Render</li>
        <li className="card p-4">UX Research, Accessibility</li>
      </ul>
    </section>
    </>
  );
}

