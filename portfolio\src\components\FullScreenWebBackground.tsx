// FullScreenWebBackground.tsx
// FIXED: Wrapper component ensuring proper full-screen web background integration
// Prevents layout shifts and ensures consistent positioning across all pages

import { lazy, Suspense } from 'react';
import { useReducedMotion } from 'framer-motion';

// Lazy load the web background for better performance
const WebBackground = lazy(() => import('./WebBackground'));

interface FullScreenWebBackgroundProps {
  variant?: 'loading' | 'home' | 'page';
  animationSpeed?: number;
  density?: 'low' | 'medium' | 'high';
  colorTheme?: 'blue' | 'red' | 'dark' | 'auto';
  enableCursorInteraction?: boolean;
  enableParticles?: boolean;
  opacity?: number;
}

export default function FullScreenWebBackground({
  variant = 'page',
  animationSpeed = 1.0,
  density = 'medium',
  colorTheme = 'auto',
  enableCursorInteraction = true,
  enableParticles = true,
  opacity = 40
}: FullScreenWebBackgroundProps) {
  const prefersReducedMotion = useReducedMotion();

  // Static fallback for reduced motion users
  if (prefersReducedMotion) {
    return (
      <div 
        className="fixed inset-0 w-screen h-screen pointer-events-none"
        style={{ 
          zIndex: -1,
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh'
        }}
      >
        <div 
          className={`w-full h-full opacity-${Math.min(opacity, 20)}`}
          style={{
            backgroundImage: `
              radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(37, 99, 235, 0.05) 100%)
            `,
            backgroundSize: '100% 100%',
            backgroundRepeat: 'no-repeat'
          }}
        />
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div 
        className="fixed inset-0 w-screen h-screen pointer-events-none"
        style={{ 
          zIndex: -1,
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh'
        }}
      />
    }>
      <WebBackground
        variant={variant}
        animationSpeed={animationSpeed}
        density={density}
        colorTheme={colorTheme}
        enableCursorInteraction={enableCursorInteraction}
        enableParticles={enableParticles}
        className={`opacity-${opacity}`}
      />
    </Suspense>
  );
}

// Utility function to get responsive opacity based on page type
export function getResponsiveOpacity(variant: 'loading' | 'home' | 'page'): number {
  switch (variant) {
    case 'loading':
      return 60;
    case 'home':
      return 40;
    case 'page':
      return 20;
    default:
      return 30;
  }
}

// Utility function to get responsive density based on device
export function getResponsiveDensity(): 'low' | 'medium' | 'high' {
  if (typeof window === 'undefined') return 'medium';
  
  const width = window.innerWidth;
  const isMobile = width < 768;
  const isTablet = width >= 768 && width < 1024;
  
  if (isMobile) return 'low';
  if (isTablet) return 'medium';
  return 'high';
}

// Utility function to get responsive animation speed
export function getResponsiveAnimationSpeed(baseSpeed: number = 1.0): number {
  if (typeof window === 'undefined') return baseSpeed;
  
  const isMobile = window.innerWidth < 768;
  const isLowEnd = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4;
  
  if (isLowEnd) return baseSpeed * 0.6;
  if (isMobile) return baseSpeed * 0.8;
  return baseSpeed;
}
