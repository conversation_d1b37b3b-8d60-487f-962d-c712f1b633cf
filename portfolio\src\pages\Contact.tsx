import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import FullScreenWebBackground, { getResponsiveOpacity, getResponsiveAnimationSpeed } from '../components/FullScreenWebBackground';

export default function Contact() {
  const [state, setState] = useState<'idle'|'loading'|'success'|'error'>('idle');
  const [msg, setMsg] = useState('');

  async function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setState('loading');
    const form = new FormData(e.currentTarget);
    // Honeypot
    if (form.get('company')) return setState('success');
    try {
      const res = await fetch('/api/contact', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: form.get('name'),
          email: form.get('email'),
          message: form.get('message'),
        })
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || 'Failed');
      setState('success');
      setMsg('Thanks! I will get back soon.');
      (e.target as HTMLFormElement).reset();
    } catch (err: any) {
      setState('error');
      setMsg(err.message);
    }
  }

  return (
    <>
      {/* FIXED: Full-screen web background for contact page */}
      <FullScreenWebBackground
        variant="page"
        animationSpeed={getResponsiveAnimationSpeed(0.4)}
        density="low"
        colorTheme="auto"
        enableCursorInteraction={true}
        enableParticles={false}
        opacity={15}
      />

      <section className="relative py-12">
      <Helmet><title>Contact — Ragul</title></Helmet>
      <h1 className="text-3xl font-bold">Contact</h1>
      <form className="mt-6 max-w-xl card p-6" onSubmit={onSubmit}>
        <label className="block">Name<input name="name" required className="mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent" /></label>
        <label className="block mt-4">Email<input type="email" name="email" required className="mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent" /></label>
        <label className="block mt-4">Message<textarea name="message" required rows={5} className="mt-1 w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-700 bg-transparent"></textarea></label>
        {/* honeypot */}
        <input type="text" name="company" tabIndex={-1} autoComplete="off" className="hidden" />
        <button disabled={state==='loading'} className="btn btn-primary mt-6">{state==='loading' ? 'Sending…' : 'Send'}</button>
        {msg && <p className="mt-3 text-sm">{msg}</p>}
      </form>
    </section>
    </>
  );
}

