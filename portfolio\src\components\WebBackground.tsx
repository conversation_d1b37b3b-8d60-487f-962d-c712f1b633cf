// WebBackground.tsx
// FIXED: Full-screen Spider-Man web background with proper viewport handling
// Features: 3D web strands, glowing effects, ambient particles, cursor interaction
// IMPROVEMENTS: Full viewport coverage, responsive scaling, proper z-indexing
// Supports: reduced motion, mobile optimization, dark mode compatibility

import { useEffect, useRef, useState, useCallback } from "react";
import { useReducedMotion } from "framer-motion";

interface WebBackgroundProps {
  variant?: 'loading' | 'home' | 'page';
  animationSpeed?: number; // 0.1 to 2.0
  density?: 'low' | 'medium' | 'high';
  colorTheme?: 'blue' | 'red' | 'dark' | 'auto';
  enableCursorInteraction?: boolean;
  enableParticles?: boolean;
  className?: string;
}

interface WebNode {
  x: number;
  y: number;
  connections: number[];
  baseX: number;
  baseY: number;
  offsetX: number;
  offsetY: number;
}

interface WebParticle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  size: number;
  opacity: number;
}

export default function WebBackground({
  variant = 'page',
  animationSpeed = 1.0,
  density = 'medium',
  colorTheme = 'auto',
  enableCursorInteraction = true,
  enableParticles = true,
  className = ''
}: WebBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>(0);
  const mouseRef = useRef({ x: 0, y: 0 });
  const nodesRef = useRef<WebNode[]>([]);
  const particlesRef = useRef<WebParticle[]>([]);
  const lastParticleTime = useRef(0);
  const resizeTimeoutRef = useRef<NodeJS.Timeout>();
  const prefersReducedMotion = useReducedMotion();
  const [isLoaded, setIsLoaded] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // FIXED: Real-time device detection for responsive behavior
  const [deviceInfo, setDeviceInfo] = useState(() => ({
    isMobile: typeof window !== 'undefined' && window.innerWidth < 768,
    isTablet: typeof window !== 'undefined' && window.innerWidth >= 768 && window.innerWidth < 1024,
    isLowEnd: typeof navigator !== 'undefined' &&
      navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4,
    pixelRatio: typeof window !== 'undefined' ? Math.min(window.devicePixelRatio || 1, 2) : 1
  }));

  // FIXED: Responsive adaptive settings based on real-time device info
  const settings = {
    nodeCount: {
      low: deviceInfo.isMobile ? 12 : deviceInfo.isTablet ? 18 : 25,
      medium: deviceInfo.isMobile ? 20 : deviceInfo.isTablet ? 30 : 40,
      high: deviceInfo.isMobile ? 30 : deviceInfo.isTablet ? 45 : 60
    }[density],
    maxConnections: deviceInfo.isMobile ? 2 : deviceInfo.isTablet ? 3 : 4,
    connectionDistance: deviceInfo.isMobile ? 120 : deviceInfo.isTablet ? 160 : 220,
    particleCount: enableParticles ? (deviceInfo.isMobile ? 6 : deviceInfo.isTablet ? 10 : 15) : 0,
    animationSpeed: deviceInfo.isLowEnd ? animationSpeed * 0.6 : animationSpeed,
    // FIXED: Responsive margins based on screen size
    margin: deviceInfo.isMobile ? 30 : deviceInfo.isTablet ? 40 : 60
  };

  // Color themes
  const getColors = useCallback(() => {
    const isDark = colorTheme === 'dark' || 
      (colorTheme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    return {
      blue: {
        web: isDark ? 'rgba(59, 130, 246, 0.4)' : 'rgba(37, 99, 235, 0.3)',
        glow: isDark ? 'rgba(59, 130, 246, 0.6)' : 'rgba(37, 99, 235, 0.5)',
        particle: isDark ? 'rgba(147, 197, 253, 0.8)' : 'rgba(96, 165, 250, 0.7)'
      },
      red: {
        web: isDark ? 'rgba(239, 68, 68, 0.4)' : 'rgba(220, 38, 38, 0.3)',
        glow: isDark ? 'rgba(239, 68, 68, 0.6)' : 'rgba(220, 38, 38, 0.5)',
        particle: isDark ? 'rgba(252, 165, 165, 0.8)' : 'rgba(248, 113, 113, 0.7)'
      },
      dark: {
        web: 'rgba(156, 163, 175, 0.3)',
        glow: 'rgba(156, 163, 175, 0.5)',
        particle: 'rgba(209, 213, 219, 0.7)'
      },
      auto: isDark ? {
        web: 'rgba(59, 130, 246, 0.4)',
        glow: 'rgba(59, 130, 246, 0.6)',
        particle: 'rgba(147, 197, 253, 0.8)'
      } : {
        web: 'rgba(37, 99, 235, 0.3)',
        glow: 'rgba(37, 99, 235, 0.5)',
        particle: 'rgba(96, 165, 250, 0.7)'
      }
    }[colorTheme];
  }, [colorTheme]);

  // FIXED: Initialize web nodes with proper viewport-based positioning
  const initializeNodes = useCallback((width: number, height: number) => {
    const nodes: WebNode[] = [];
    const margin = settings.margin;

    // FIXED: Ensure nodes are distributed across the entire viewport
    const effectiveWidth = Math.max(width - 2 * margin, 100);
    const effectiveHeight = Math.max(height - 2 * margin, 100);

    for (let i = 0; i < settings.nodeCount; i++) {
      // FIXED: Better distribution algorithm for different aspect ratios
      const x = margin + Math.random() * effectiveWidth;
      const y = margin + Math.random() * effectiveHeight;

      nodes.push({
        x,
        y,
        baseX: x,
        baseY: y,
        offsetX: 0,
        offsetY: 0,
        connections: []
      });
    }

    // Create connections based on distance
    nodes.forEach((node, i) => {
      const distances = nodes.map((other, j) => ({
        index: j,
        distance: Math.hypot(node.x - other.x, node.y - other.y)
      })).filter(d => d.index !== i).sort((a, b) => a.distance - b.distance);

      node.connections = distances
        .slice(0, settings.maxConnections)
        .filter(d => d.distance < settings.connectionDistance)
        .map(d => d.index);
    });

    nodesRef.current = nodes;
  }, [settings.nodeCount, settings.maxConnections, settings.connectionDistance, settings.margin]);

  // FIXED: Create particle with proper viewport-based positioning
  const createParticle = useCallback((x?: number, y?: number) => {
    if (!dimensions.width || !dimensions.height) return;

    const particle: WebParticle = {
      x: x ?? Math.random() * dimensions.width,
      y: y ?? Math.random() * dimensions.height,
      vx: (Math.random() - 0.5) * (deviceInfo.isMobile ? 0.3 : 0.5),
      vy: (Math.random() - 0.5) * (deviceInfo.isMobile ? 0.3 : 0.5),
      life: 0,
      maxLife: 2000 + Math.random() * 3000,
      size: (deviceInfo.isMobile ? 0.8 : 1) + Math.random() * (deviceInfo.isMobile ? 1.5 : 2),
      opacity: 0
    };

    particlesRef.current.push(particle);
  }, [dimensions.width, dimensions.height, deviceInfo.isMobile]);

  // FIXED: Handle mouse movement with proper viewport coordinates
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!enableCursorInteraction) return;

    // FIXED: Use viewport coordinates instead of canvas bounding rect
    // This ensures mouse interaction works correctly when canvas is full-screen
    mouseRef.current = {
      x: e.clientX,
      y: e.clientY
    };
  }, [enableCursorInteraction]);

  // FIXED: Optimized animation loop with proper viewport handling
  const animate = useCallback((timestamp: number) => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || !dimensions.width || !dimensions.height) return;

    const colors = getColors();
    const deltaTime = timestamp - (animationRef.current || timestamp);
    animationRef.current = timestamp;

    // FIXED: Clear entire viewport canvas
    ctx.clearRect(0, 0, dimensions.width, dimensions.height);

    // Update nodes with ambient movement and cursor interaction
    nodesRef.current.forEach((node, i) => {
      // Ambient floating motion
      const time = timestamp * 0.001 * settings.animationSpeed;
      node.offsetX = Math.sin(time + i * 0.5) * 8;
      node.offsetY = Math.cos(time * 0.7 + i * 0.3) * 6;

      // Cursor interaction
      if (enableCursorInteraction) {
        const dx = mouseRef.current.x - node.baseX;
        const dy = mouseRef.current.y - node.baseY;
        const distance = Math.hypot(dx, dy);
        
        if (distance < 150) {
          const force = (150 - distance) / 150;
          node.offsetX += dx * force * 0.1;
          node.offsetY += dy * force * 0.1;
        }
      }

      node.x = node.baseX + node.offsetX;
      node.y = node.baseY + node.offsetY;
    });

    // Draw web connections
    ctx.strokeStyle = colors.web;
    ctx.lineWidth = 1.5;
    ctx.shadowColor = colors.glow;
    ctx.shadowBlur = 3;

    nodesRef.current.forEach((node, i) => {
      node.connections.forEach(connectionIndex => {
        if (connectionIndex > i) { // Avoid drawing twice
          const target = nodesRef.current[connectionIndex];
          
          ctx.beginPath();
          ctx.moveTo(node.x, node.y);
          ctx.lineTo(target.x, target.y);
          ctx.stroke();
        }
      });
    });

    // Reset shadow for particles
    ctx.shadowBlur = 0;

    // Update and draw particles
    if (enableParticles) {
      // Create new particles occasionally
      if (timestamp - lastParticleTime.current > 800 + Math.random() * 1200) {
        createParticle();
        lastParticleTime.current = timestamp;
      }

      // Update existing particles
      particlesRef.current = particlesRef.current.filter(particle => {
        particle.life += deltaTime;
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Fade in/out
        const lifeRatio = particle.life / particle.maxLife;
        if (lifeRatio < 0.1) {
          particle.opacity = lifeRatio * 10;
        } else if (lifeRatio > 0.9) {
          particle.opacity = (1 - lifeRatio) * 10;
        } else {
          particle.opacity = 1;
        }

        // Draw particle
        ctx.fillStyle = colors.particle.replace(/[\d.]+\)$/, `${particle.opacity * 0.8})`);
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();

        return particle.life < particle.maxLife;
      });
    }

    requestAnimationFrame(animate);
  }, [getColors, settings.animationSpeed, enableCursorInteraction, enableParticles, createParticle, dimensions.width, dimensions.height]);

  // FIXED: Proper viewport-based resize handling
  const updateDimensions = useCallback(() => {
    // FIXED: Use viewport dimensions instead of container dimensions
    const width = window.innerWidth;
    const height = window.innerHeight;

    setDimensions({ width, height });

    // Update device info on resize
    setDeviceInfo({
      isMobile: width < 768,
      isTablet: width >= 768 && width < 1024,
      isLowEnd: navigator.hardwareConcurrency ? navigator.hardwareConcurrency <= 4 : false,
      pixelRatio: Math.min(window.devicePixelRatio || 1, 2)
    });

    return { width, height };
  }, []);

  // FIXED: Optimized resize handler with debouncing
  const handleResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const { width, height } = updateDimensions();
      const dpr = Math.min(window.devicePixelRatio || 1, 2);

      // FIXED: Set canvas to full viewport size
      canvas.width = width * dpr;
      canvas.height = height * dpr;
      ctx.scale(dpr, dpr);

      // FIXED: Ensure canvas covers full viewport
      canvas.style.width = width + 'px';
      canvas.style.height = height + 'px';

      // Reinitialize nodes with new dimensions
      initializeNodes(width, height);
    }, 100); // Debounce resize events
  }, [updateDimensions, initializeNodes]);

  // FIXED: Setup canvas and start animation with proper viewport handling
  useEffect(() => {
    if (prefersReducedMotion) {
      setIsLoaded(true);
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Initial setup
    handleResize();

    // Event listeners
    window.addEventListener('resize', handleResize, { passive: true });

    if (enableCursorInteraction) {
      window.addEventListener('mousemove', handleMouseMove, { passive: true });
    }

    // Start animation
    requestAnimationFrame(animate);
    setIsLoaded(true);

    return () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [prefersReducedMotion, handleResize, handleMouseMove, animate, enableCursorInteraction]);

  if (prefersReducedMotion) {
    // FIXED: Static SVG fallback with proper full-screen positioning
    return (
      <div
        ref={containerRef}
        className={`fixed inset-0 w-screen h-screen ${className}`}
        style={{
          zIndex: -1,
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          pointerEvents: 'none'
        }}
      >
        <svg
          className="w-full h-full opacity-20"
          viewBox="0 0 800 600"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <pattern id="web-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <path
                d="M50 10 L90 50 L50 90 L10 50 Z M50 10 L50 90 M10 50 L90 50"
                stroke="currentColor"
                strokeWidth="1"
                fill="none"
                opacity="0.3"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#web-pattern)" />
        </svg>
      </div>
    );
  }

  // FIXED: Canvas with proper full-screen positioning and styling
  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 w-screen h-screen ${className}`}
      style={{
        zIndex: -1,
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        pointerEvents: 'none'
      }}
    >
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.5s ease-in-out',
          display: 'block',
          width: '100%',
          height: '100%'
        }}
        aria-hidden="true"
      />
    </div>
  );
}
