// WebBackground.tsx
// Spider-Man inspired animated web background with performance optimizations
// Features: 3D web strands, glowing effects, ambient particles, cursor interaction
// Supports: reduced motion, mobile optimization, dark mode compatibility

import { useEffect, useRef, useState, useCallback } from "react";
import { useReducedMotion } from "framer-motion";

interface WebBackgroundProps {
  variant?: 'loading' | 'home' | 'page';
  animationSpeed?: number; // 0.1 to 2.0
  density?: 'low' | 'medium' | 'high';
  colorTheme?: 'blue' | 'red' | 'dark' | 'auto';
  enableCursorInteraction?: boolean;
  enableParticles?: boolean;
  className?: string;
}

interface WebNode {
  x: number;
  y: number;
  connections: number[];
  baseX: number;
  baseY: number;
  offsetX: number;
  offsetY: number;
}

interface WebParticle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  size: number;
  opacity: number;
}

export default function WebBackground({
  variant = 'page',
  animationSpeed = 1.0,
  density = 'medium',
  colorTheme = 'auto',
  enableCursorInteraction = true,
  enableParticles = true,
  className = ''
}: WebBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);
  const mouseRef = useRef({ x: 0, y: 0 });
  const nodesRef = useRef<WebNode[]>([]);
  const particlesRef = useRef<WebParticle[]>([]);
  const lastParticleTime = useRef(0);
  const prefersReducedMotion = useReducedMotion();
  const [isLoaded, setIsLoaded] = useState(false);

  // Performance settings based on device
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const isLowEnd = typeof navigator !== 'undefined' && 
    navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 4;

  // Adaptive settings
  const settings = {
    nodeCount: {
      low: isMobile ? 15 : 20,
      medium: isMobile ? 25 : 35,
      high: isMobile ? 35 : 50
    }[density],
    maxConnections: isMobile ? 3 : 4,
    connectionDistance: isMobile ? 150 : 200,
    particleCount: enableParticles ? (isMobile ? 8 : 15) : 0,
    animationSpeed: isLowEnd ? animationSpeed * 0.7 : animationSpeed
  };

  // Color themes
  const getColors = useCallback(() => {
    const isDark = colorTheme === 'dark' || 
      (colorTheme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    return {
      blue: {
        web: isDark ? 'rgba(59, 130, 246, 0.4)' : 'rgba(37, 99, 235, 0.3)',
        glow: isDark ? 'rgba(59, 130, 246, 0.6)' : 'rgba(37, 99, 235, 0.5)',
        particle: isDark ? 'rgba(147, 197, 253, 0.8)' : 'rgba(96, 165, 250, 0.7)'
      },
      red: {
        web: isDark ? 'rgba(239, 68, 68, 0.4)' : 'rgba(220, 38, 38, 0.3)',
        glow: isDark ? 'rgba(239, 68, 68, 0.6)' : 'rgba(220, 38, 38, 0.5)',
        particle: isDark ? 'rgba(252, 165, 165, 0.8)' : 'rgba(248, 113, 113, 0.7)'
      },
      dark: {
        web: 'rgba(156, 163, 175, 0.3)',
        glow: 'rgba(156, 163, 175, 0.5)',
        particle: 'rgba(209, 213, 219, 0.7)'
      },
      auto: isDark ? {
        web: 'rgba(59, 130, 246, 0.4)',
        glow: 'rgba(59, 130, 246, 0.6)',
        particle: 'rgba(147, 197, 253, 0.8)'
      } : {
        web: 'rgba(37, 99, 235, 0.3)',
        glow: 'rgba(37, 99, 235, 0.5)',
        particle: 'rgba(96, 165, 250, 0.7)'
      }
    }[colorTheme];
  }, [colorTheme]);

  // Initialize web nodes
  const initializeNodes = useCallback((width: number, height: number) => {
    const nodes: WebNode[] = [];
    const margin = 50;
    
    for (let i = 0; i < settings.nodeCount; i++) {
      const x = margin + Math.random() * (width - 2 * margin);
      const y = margin + Math.random() * (height - 2 * margin);
      
      nodes.push({
        x,
        y,
        baseX: x,
        baseY: y,
        offsetX: 0,
        offsetY: 0,
        connections: []
      });
    }

    // Create connections based on distance
    nodes.forEach((node, i) => {
      const distances = nodes.map((other, j) => ({
        index: j,
        distance: Math.hypot(node.x - other.x, node.y - other.y)
      })).filter(d => d.index !== i).sort((a, b) => a.distance - b.distance);

      node.connections = distances
        .slice(0, settings.maxConnections)
        .filter(d => d.distance < settings.connectionDistance)
        .map(d => d.index);
    });

    nodesRef.current = nodes;
  }, [settings.nodeCount, settings.maxConnections, settings.connectionDistance]);

  // Create particle
  const createParticle = useCallback((x?: number, y?: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const particle: WebParticle = {
      x: x ?? Math.random() * canvas.width,
      y: y ?? Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      life: 0,
      maxLife: 2000 + Math.random() * 3000,
      size: 1 + Math.random() * 2,
      opacity: 0
    };

    particlesRef.current.push(particle);
  }, []);

  // Handle mouse movement
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!enableCursorInteraction) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    mouseRef.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  }, [enableCursorInteraction]);

  // Animation loop
  const animate = useCallback((timestamp: number) => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    const colors = getColors();
    const deltaTime = timestamp - (animationRef.current || timestamp);
    animationRef.current = timestamp;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update nodes with ambient movement and cursor interaction
    nodesRef.current.forEach((node, i) => {
      // Ambient floating motion
      const time = timestamp * 0.001 * settings.animationSpeed;
      node.offsetX = Math.sin(time + i * 0.5) * 8;
      node.offsetY = Math.cos(time * 0.7 + i * 0.3) * 6;

      // Cursor interaction
      if (enableCursorInteraction) {
        const dx = mouseRef.current.x - node.baseX;
        const dy = mouseRef.current.y - node.baseY;
        const distance = Math.hypot(dx, dy);
        
        if (distance < 150) {
          const force = (150 - distance) / 150;
          node.offsetX += dx * force * 0.1;
          node.offsetY += dy * force * 0.1;
        }
      }

      node.x = node.baseX + node.offsetX;
      node.y = node.baseY + node.offsetY;
    });

    // Draw web connections
    ctx.strokeStyle = colors.web;
    ctx.lineWidth = 1.5;
    ctx.shadowColor = colors.glow;
    ctx.shadowBlur = 3;

    nodesRef.current.forEach((node, i) => {
      node.connections.forEach(connectionIndex => {
        if (connectionIndex > i) { // Avoid drawing twice
          const target = nodesRef.current[connectionIndex];
          
          ctx.beginPath();
          ctx.moveTo(node.x, node.y);
          ctx.lineTo(target.x, target.y);
          ctx.stroke();
        }
      });
    });

    // Reset shadow for particles
    ctx.shadowBlur = 0;

    // Update and draw particles
    if (enableParticles) {
      // Create new particles occasionally
      if (timestamp - lastParticleTime.current > 800 + Math.random() * 1200) {
        createParticle();
        lastParticleTime.current = timestamp;
      }

      // Update existing particles
      particlesRef.current = particlesRef.current.filter(particle => {
        particle.life += deltaTime;
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Fade in/out
        const lifeRatio = particle.life / particle.maxLife;
        if (lifeRatio < 0.1) {
          particle.opacity = lifeRatio * 10;
        } else if (lifeRatio > 0.9) {
          particle.opacity = (1 - lifeRatio) * 10;
        } else {
          particle.opacity = 1;
        }

        // Draw particle
        ctx.fillStyle = colors.particle.replace(/[\d.]+\)$/, `${particle.opacity * 0.8})`);
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();

        return particle.life < particle.maxLife;
      });
    }

    requestAnimationFrame(animate);
  }, [getColors, settings.animationSpeed, enableCursorInteraction, enableParticles, createParticle]);

  // Setup canvas and start animation
  useEffect(() => {
    if (prefersReducedMotion) {
      setIsLoaded(true);
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const handleResize = () => {
      const rect = canvas.getBoundingClientRect();
      const dpr = Math.min(window.devicePixelRatio || 1, 2);
      
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      ctx.scale(dpr, dpr);
      
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';

      initializeNodes(rect.width, rect.height);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    
    if (enableCursorInteraction) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    // Start animation
    requestAnimationFrame(animate);
    setIsLoaded(true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [prefersReducedMotion, initializeNodes, handleMouseMove, animate, enableCursorInteraction]);

  if (prefersReducedMotion) {
    // Static SVG fallback for reduced motion
    return (
      <div className={`absolute inset-0 -z-10 ${className}`}>
        <svg
          className="w-full h-full opacity-20"
          viewBox="0 0 800 600"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <pattern id="web-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <path
                d="M50 10 L90 50 L50 90 L10 50 Z M50 10 L50 90 M10 50 L90 50"
                stroke="currentColor"
                strokeWidth="1"
                fill="none"
                opacity="0.3"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#web-pattern)" />
        </svg>
      </div>
    );
  }

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 -z-10 ${className}`}
      style={{ opacity: isLoaded ? 1 : 0, transition: 'opacity 0.5s ease-in-out' }}
      aria-hidden="true"
    />
  );
}
