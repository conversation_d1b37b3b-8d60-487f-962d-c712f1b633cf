// WebBackgroundDemo.tsx
// Demo component to showcase all Spider-Man web background features
// Use this to test different configurations and variants

import { useState } from "react";
import WebBackground from "./WebBackground";
import SpiderManSilhouette from "./SpiderManSilhouette";
import WebShotEasterEgg from "./WebShotEasterEgg";

type Variant = 'loading' | 'home' | 'page';
type Density = 'low' | 'medium' | 'high';
type ColorTheme = 'blue' | 'red' | 'dark' | 'auto';

export default function WebBackgroundDemo() {
  const [variant, setVariant] = useState<Variant>('home');
  const [animationSpeed, setAnimationSpeed] = useState(1.0);
  const [density, setDensity] = useState<Density>('medium');
  const [colorTheme, setColorTheme] = useState<ColorTheme>('auto');
  const [enableCursor, setEnableCursor] = useState(true);
  const [enableParticles, setEnableParticles] = useState(true);
  const [enableSpiderMan, setEnableSpiderMan] = useState(false);
  const [opacity, setOpacity] = useState(40);

  const triggerSpiderMan = () => {
    setEnableSpiderMan(false);
    setTimeout(() => setEnableSpiderMan(true), 100);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Web Background */}
      <WebBackground 
        variant={variant}
        animationSpeed={animationSpeed}
        density={density}
        colorTheme={colorTheme}
        enableCursorInteraction={enableCursor}
        enableParticles={enableParticles}
        className={`opacity-${opacity}`}
      />

      {/* Spider-Man Silhouette */}
      {enableSpiderMan && (
        <SpiderManSilhouette 
          enabled={true}
          delay={500}
          className="z-10"
        />
      )}

      {/* Easter Egg */}
      <WebShotEasterEgg />

      {/* Demo Controls */}
      <div className="relative z-20 p-6 max-w-4xl mx-auto">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg p-6 mb-6">
          <h1 className="text-3xl font-bold mb-4">🕷️ Spider-Man Web Background Demo</h1>
          <p className="text-gray-300 mb-4">
            Interactive demo of the Spider-Man inspired web background system. 
            Try different settings and <strong>double-click anywhere</strong> for the easter egg!
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Variant Control */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Variant</h3>
            <div className="space-y-2">
              {(['loading', 'home', 'page'] as Variant[]).map(v => (
                <label key={v} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={variant === v}
                    onChange={() => setVariant(v)}
                    className="text-blue-500"
                  />
                  <span className="capitalize">{v}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Animation Speed */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Animation Speed</h3>
            <input
              type="range"
              min="0.1"
              max="2.0"
              step="0.1"
              value={animationSpeed}
              onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}
              className="w-full"
            />
            <div className="text-sm text-gray-300 mt-1">{animationSpeed.toFixed(1)}x</div>
          </div>

          {/* Density */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Density</h3>
            <div className="space-y-2">
              {(['low', 'medium', 'high'] as Density[]).map(d => (
                <label key={d} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={density === d}
                    onChange={() => setDensity(d)}
                    className="text-blue-500"
                  />
                  <span className="capitalize">{d}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Color Theme */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Color Theme</h3>
            <div className="space-y-2">
              {(['auto', 'blue', 'red', 'dark'] as ColorTheme[]).map(c => (
                <label key={c} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={colorTheme === c}
                    onChange={() => setColorTheme(c)}
                    className="text-blue-500"
                  />
                  <span className="capitalize">{c}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Opacity */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Opacity</h3>
            <input
              type="range"
              min="10"
              max="100"
              step="10"
              value={opacity}
              onChange={(e) => setOpacity(parseInt(e.target.value))}
              className="w-full"
            />
            <div className="text-sm text-gray-300 mt-1">{opacity}%</div>
          </div>

          {/* Features */}
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-3">Features</h3>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={enableCursor}
                  onChange={(e) => setEnableCursor(e.target.checked)}
                  className="text-blue-500"
                />
                <span>Cursor Interaction</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={enableParticles}
                  onChange={(e) => setEnableParticles(e.target.checked)}
                  className="text-blue-500"
                />
                <span>Particles</span>
              </label>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex flex-wrap gap-4">
          <button
            onClick={triggerSpiderMan}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors"
          >
            🕷️ Trigger Spider-Man Swing
          </button>
          
          <div className="px-6 py-3 bg-gray-700 rounded-lg">
            <span className="text-sm">💡 Double-click anywhere for web shot!</span>
          </div>
        </div>

        {/* Feature Showcase */}
        <div className="mt-8 bg-black/50 backdrop-blur-sm rounded-lg p-6">
          <h2 className="text-xl font-bold mb-4">✨ Features Showcase</h2>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-blue-400 mb-2">Performance</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Adaptive particle count for mobile</li>
                <li>• Frame rate throttling (30-60fps)</li>
                <li>• Distance-based optimizations</li>
                <li>• Memory-efficient cleanup</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-400 mb-2">Accessibility</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Reduced motion support</li>
                <li>• Static SVG fallback</li>
                <li>• High contrast options</li>
                <li>• Keyboard navigation friendly</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-400 mb-2">Interactions</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Mouse cursor attraction</li>
                <li>• Double-click web shots</li>
                <li>• Ambient floating motion</li>
                <li>• Particle breathing effects</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-400 mb-2">Customization</h4>
              <ul className="space-y-1 text-gray-300">
                <li>• Multiple color themes</li>
                <li>• Configurable density</li>
                <li>• Variable animation speed</li>
                <li>• Page-specific variants</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
