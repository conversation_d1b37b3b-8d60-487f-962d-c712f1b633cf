// CinematicLoadingPage.tsx
// Marvel-inspired cinematic loading page with "R" logo centerpiece
// Features: 3D metallic logo, spider-web background, Arc Reactor spinner, particle effects

import { useCallback, useEffect, useRef, useState } from "react";
import { motion, useAnimationControls, useMotionValue } from "framer-motion";

interface CinematicLoadingPageProps {
  onFinish?: () => void;
  duration?: number;
  logoImageUrl?: string;
}

export default function CinematicLoadingPage({ 
  onFinish, 
  duration = 5000,
  logoImageUrl = "/images/r-logo.png" // Default path for R logo
}: CinematicLoadingPageProps) {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showText, setShowText] = useState(false);
  const startTimeRef = useRef<number | null>(null);
  const controls = useAnimationControls();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const progressValue = useMotionValue(0);

  // Typewriter text
  const [typedText, setTypedText] = useState("");
  const fullText = "Assembling your experience...";

  // Progress tracking
  useEffect(() => {
    let animationFrame: number;
    
    const updateProgress = (timestamp: number) => {
      if (!startTimeRef.current) {
        startTimeRef.current = timestamp;
      }
      
      const elapsed = timestamp - startTimeRef.current;
      const newProgress = Math.min(elapsed / duration, 1);
      
      setProgress(newProgress);
      progressValue.set(newProgress);
      
      if (newProgress < 1) {
        animationFrame = requestAnimationFrame(updateProgress);
      } else {
        setIsComplete(true);
      }
    };
    
    animationFrame = requestAnimationFrame(updateProgress);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [duration, progressValue]);

  // Show text after logo appears
  useEffect(() => {
    const timer = setTimeout(() => setShowText(true), 1500);
    return () => clearTimeout(timer);
  }, []);

  // Typewriter effect
  useEffect(() => {
    if (!showText) return;
    
    let index = 0;
    const typeInterval = setInterval(() => {
      if (index <= fullText.length) {
        setTypedText(fullText.slice(0, index));
        index++;
      } else {
        clearInterval(typeInterval);
      }
    }, 80);
    
    return () => clearInterval(typeInterval);
  }, [showText, fullText]);

  // Handle completion and transition
  useEffect(() => {
    if (!isComplete) return;
    
    const exitAnimation = async () => {
      await controls.start({
        opacity: 0,
        scale: 0.8,
        transition: { duration: 1, ease: [0.22, 1, 0.36, 1] }
      });
      
      onFinish?.();
    };
    
    const timer = setTimeout(exitAnimation, 800);
    return () => clearTimeout(timer);
  }, [isComplete, controls, onFinish]);

  // Spider web canvas animation
  const initializeSpiderWeb = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let animationId: number;
    let time = 0;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const drawSpiderWeb = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      
      // Set web style
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;
      ctx.shadowColor = 'rgba(255, 255, 255, 0.2)';
      ctx.shadowBlur = 2;

      // Draw radial web lines
      const numRadialLines = 12;
      for (let i = 0; i < numRadialLines; i++) {
        const angle = (i * Math.PI * 2) / numRadialLines + time * 0.001;
        const maxRadius = Math.max(canvas.width, canvas.height);
        
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(
          centerX + Math.cos(angle) * maxRadius,
          centerY + Math.sin(angle) * maxRadius
        );
        ctx.stroke();
      }

      // Draw concentric web circles
      const numCircles = 8;
      const maxRadius = Math.min(canvas.width, canvas.height) * 0.6;
      
      for (let i = 1; i <= numCircles; i++) {
        const radius = (maxRadius / numCircles) * i + Math.sin(time * 0.002 + i) * 10;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();
      }

      time += 16; // ~60fps
      animationId = requestAnimationFrame(drawSpiderWeb);
    };

    resizeCanvas();
    drawSpiderWeb();

    const handleResize = () => resizeCanvas();
    window.addEventListener('resize', handleResize);

    return () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const cleanup = initializeSpiderWeb();
    return cleanup;
  }, [initializeSpiderWeb]);

  return (
    <motion.div
      className="fixed inset-0 w-screen h-screen flex flex-col items-center justify-center overflow-hidden"
      style={{ 
        background: 'linear-gradient(135deg, #000000 0%, #1a0000 30%, #000033 70%, #000000 100%)',
        zIndex: 9999 
      }}
      animate={controls}
      initial={{ opacity: 1 }}
    >
      {/* Spider Web Canvas Background */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ zIndex: 1 }}
      />

      {/* Floating Particles */}
      <div className="absolute inset-0" style={{ zIndex: 2 }}>
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Light Streaks */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-32 bg-gradient-to-b from-transparent via-blue-400 to-transparent opacity-60"
          style={{
            left: `${20 + i * 30}%`,
            top: '-10%',
            transform: 'rotate(15deg)'
          }}
          animate={{
            y: ['0vh', '110vh'],
            opacity: [0, 0.6, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 2,
            ease: "linear"
          }}
        />
      ))}

      {/* Main Content Container */}
      <div className="relative z-10 flex flex-col items-center">
        {/* 3D Metallic "R" Logo */}
        <motion.div
          className="relative mb-8"
          initial={{ opacity: 0, scale: 0.5, rotateY: -180 }}
          animate={{ 
            opacity: 1, 
            scale: 1, 
            rotateY: 0,
          }}
          transition={{ 
            duration: 1.5, 
            ease: [0.22, 1, 0.36, 1],
            delay: 0.5 
          }}
        >
          {/* Logo Container with 3D Effects */}
          <div className="relative w-32 h-32 md:w-40 md:h-40 lg:w-48 lg:h-48">
            {/* Glow Effect */}
            <motion.div
              className="absolute inset-0 rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(147, 51, 234, 0.6) 0%, rgba(147, 51, 234, 0.3) 40%, transparent 70%)',
                filter: 'blur(20px)',
              }}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.6, 1, 0.6]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            {/* Main Logo */}
            <motion.div
              className="relative w-full h-full flex items-center justify-center text-8xl md:text-9xl lg:text-[8rem] font-black text-purple-500"
              style={{
                fontFamily: "'Inter', sans-serif",
                textShadow: `
                  0 0 20px rgba(147, 51, 234, 0.8),
                  0 0 40px rgba(147, 51, 234, 0.6),
                  0 0 60px rgba(147, 51, 234, 0.4),
                  0 10px 20px rgba(0, 0, 0, 0.5)
                `,
                filter: 'drop-shadow(0 0 30px rgba(147, 51, 234, 0.7))'
              }}
              animate={{
                textShadow: [
                  '0 0 20px rgba(147, 51, 234, 0.8), 0 0 40px rgba(147, 51, 234, 0.6), 0 0 60px rgba(147, 51, 234, 0.4)',
                  '0 0 30px rgba(147, 51, 234, 1), 0 0 60px rgba(147, 51, 234, 0.8), 0 0 90px rgba(147, 51, 234, 0.6)',
                  '0 0 20px rgba(147, 51, 234, 0.8), 0 0 40px rgba(147, 51, 234, 0.6), 0 0 60px rgba(147, 51, 234, 0.4)'
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              R
            </motion.div>

            {/* Light Sweep Effect */}
            <motion.div
              className="absolute inset-0 overflow-hidden rounded-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
                style={{ 
                  width: '30%',
                  height: '100%',
                  transform: 'skewX(-20deg)'
                }}
                animate={{
                  x: ['-100%', '300%']
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatDelay: 2,
                  ease: "easeInOut"
                }}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Arc Reactor Loading Spinner */}
        <motion.div
          className="relative w-24 h-24 mb-6"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.5, duration: 0.8 }}
        >
          {/* Outer Ring */}
          <div className="absolute inset-0 rounded-full border-2 border-purple-500/30" />
          
          {/* Progress Ring */}
          <svg className="absolute inset-0 w-full h-full transform -rotate-90">
            <circle
              cx="50%"
              cy="50%"
              r="44"
              fill="none"
              stroke="rgba(147, 51, 234, 0.8)"
              strokeWidth="3"
              strokeLinecap="round"
              strokeDasharray={276.46}
              strokeDashoffset={276.46 * (1 - progress)}
              style={{
                filter: 'drop-shadow(0 0 10px rgba(147, 51, 234, 0.8))',
                transition: 'stroke-dashoffset 0.3s ease-out'
              }}
            />
          </svg>
          
          {/* Center Core */}
          <motion.div
            className="absolute inset-4 rounded-full bg-purple-500/20 border border-purple-400/50"
            animate={{
              boxShadow: [
                '0 0 20px rgba(147, 51, 234, 0.5)',
                '0 0 40px rgba(147, 51, 234, 0.8)',
                '0 0 20px rgba(147, 51, 234, 0.5)'
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </motion.div>

        {/* Loading Text with Typewriter Effect */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: showText ? 1 : 0, y: showText ? 0 : 20 }}
          transition={{ duration: 0.8 }}
        >
          <motion.p
            className="text-lg md:text-xl text-white font-light tracking-wider"
            style={{ 
              fontFamily: "'Orbitron', 'Bebas Neue', sans-serif",
              textShadow: '0 0 10px rgba(255, 255, 255, 0.5)'
            }}
            animate={{
              opacity: [1, 0.7, 1]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            {typedText}
            <motion.span
              className="inline-block w-0.5 h-6 bg-purple-400 ml-1"
              animate={{ opacity: [1, 0] }}
              transition={{ duration: 0.8, repeat: Infinity }}
            />
          </motion.p>
          
          {/* Progress Percentage */}
          <motion.p
            className="text-sm text-purple-300 mt-2 font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            {Math.round(progress * 100)}%
          </motion.p>
        </motion.div>
      </div>
    </motion.div>
  );
}
