import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import FullScreenWebBackground, { getResponsiveOpacity, getResponsiveDensity, getResponsiveAnimationSpeed } from '../components/FullScreenWebBackground';

export default function Home() {
  return (
    <>
      {/* FIXED: Full-screen Spider-Man web background */}
      <FullScreenWebBackground
        variant="home"
        animationSpeed={getResponsiveAnimationSpeed(0.8)}
        density={getResponsiveDensity()}
        colorTheme="auto"
        enableCursorInteraction={true}
        enableParticles={true}
        opacity={getResponsiveOpacity('home')}
      />

      <section className="relative py-12 sm:py-16">
      <Helmet>
        <title>Ragul — Web & App Developer</title>
        <meta name="description" content="Portfolio of Ragul, a web & mobile developer crafting fast, accessible experiences." />
        <script type="application/ld+json">{JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'Person',
          name: '<PERSON>gu<PERSON>',
          url: 'https://ragul.dev',
          jobTitle: 'Web & App Developer',
        })}</script>
      </Helmet>
      <div className="grid gap-10 lg:grid-cols-2 items-center">
        <div>
          <motion.h1 initial={{ opacity: 0, y: 12 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: .4 }} className="text-4xl sm:text-5xl font-extrabold tracking-tight">
            Hi, I’m Ragul — I build crisp, performant web & mobile apps
          </motion.h1>
          <p className="mt-4 text-neutral-600 dark:text-neutral-300 max-w-prose">From idea to production, I ship delightful experiences with React, TypeScript, Node, and a sprinkle of motion.</p>
          <div className="mt-6 flex flex-wrap gap-3">
            <Link to="/projects" className="btn btn-primary">View Projects</Link>
            <Link to="/contact" className="btn btn-ghost">Contact Me</Link>
          </div>
          <p className="mt-3 text-xs text-neutral-500">Tanglish: namma appu build pannalaama? 😄</p>
        </div>
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: .2 }} className="card p-6">
          <h2 className="font-semibold">Quick stats</h2>
          <ul className="mt-3 grid grid-cols-2 gap-3 text-sm">
            <li><span className="text-2xl font-extrabold">25+</span><br/>Projects shipped</li>
            <li><span className="text-2xl font-extrabold">90+</span><br/>Lighthouse perf</li>
            <li><span className="text-2xl font-extrabold">5</span><br/>Years building</li>
            <li><span className="text-2xl font-extrabold">∞</span><br/>Curiosity</li>
          </ul>
        </motion.div>
      </div>
    </section>
    </>
  );
}

