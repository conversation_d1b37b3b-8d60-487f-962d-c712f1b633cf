/* cinematic-loading.css */
/* Enhanced CSS for Marvel-inspired cinematic loading page */
/* 3D metallic effects, neon glows, and performance optimizations */

/* Import futuristic fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Bebas+Neue&display=swap');

/* Base loading container */
.cinematic-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  overflow: hidden;
  background: linear-gradient(135deg, #000000 0%, #1a0000 30%, #000033 70%, #000000 100%);
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 3D Metallic Logo Styles */
.metallic-logo {
  font-family: 'Inter', 'Helvetica Neue', sans-serif;
  font-weight: 900;
  background: linear-gradient(
    145deg,
    #9333ea 0%,
    #c084fc 25%,
    #e879f9 50%,
    #c084fc 75%,
    #9333ea 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 
    0 0 20px rgba(147, 51, 234, 0.8),
    0 0 40px rgba(147, 51, 234, 0.6),
    0 0 60px rgba(147, 51, 234, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.5);
  filter: drop-shadow(0 0 30px rgba(147, 51, 234, 0.7));
  will-change: transform, filter, text-shadow;
  transform: translateZ(0);
}

/* Enhanced 3D effect for logo */
.metallic-logo::before {
  content: 'R';
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: -1;
  background: linear-gradient(
    145deg,
    rgba(147, 51, 234, 0.3) 0%,
    rgba(192, 132, 252, 0.2) 50%,
    rgba(147, 51, 234, 0.3) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: blur(1px);
}

/* Neon glow animation */
@keyframes neon-pulse {
  0%, 100% {
    text-shadow: 
      0 0 20px rgba(147, 51, 234, 0.8),
      0 0 40px rgba(147, 51, 234, 0.6),
      0 0 60px rgba(147, 51, 234, 0.4);
    filter: drop-shadow(0 0 30px rgba(147, 51, 234, 0.7));
  }
  50% {
    text-shadow: 
      0 0 30px rgba(147, 51, 234, 1),
      0 0 60px rgba(147, 51, 234, 0.8),
      0 0 90px rgba(147, 51, 234, 0.6);
    filter: drop-shadow(0 0 50px rgba(147, 51, 234, 1));
  }
}

.neon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

/* Light sweep effect */
@keyframes light-sweep {
  0% {
    transform: translateX(-100%) skewX(-20deg);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(300%) skewX(-20deg);
    opacity: 0;
  }
}

.light-sweep {
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: light-sweep 3s ease-in-out infinite;
  animation-delay: 2s;
}

/* Spider web canvas optimizations */
.spider-web-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: auto;
  transform: translateZ(0);
  opacity: 0.8;
}

/* Arc Reactor Spinner */
.arc-reactor {
  position: relative;
  width: 96px;
  height: 96px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(147, 51, 234, 0.1) 0%,
    rgba(147, 51, 234, 0.05) 50%,
    transparent 100%
  );
  box-shadow: 
    0 0 20px rgba(147, 51, 234, 0.3),
    inset 0 0 20px rgba(147, 51, 234, 0.1);
}

.arc-reactor::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(147, 51, 234, 0.3) 0%,
    rgba(147, 51, 234, 0.1) 70%,
    transparent 100%
  );
  animation: arc-core-pulse 2s ease-in-out infinite;
}

@keyframes arc-core-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(147, 51, 234, 0.8);
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* Progress ring animation */
.progress-ring {
  transform: rotate(-90deg);
  will-change: stroke-dashoffset;
}

.progress-ring circle {
  transition: stroke-dashoffset 0.3s ease-out;
  filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.8));
}

/* Floating particles */
.floating-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  will-change: transform, opacity;
  transform: translateZ(0);
}

@keyframes float-up {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-50px) scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0.3;
  }
}

.particle-float {
  animation: float-up 4s ease-in-out infinite;
}

/* Light streaks */
.light-streak {
  position: absolute;
  width: 2px;
  height: 128px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(96, 165, 250, 0.6) 50%,
    transparent 100%
  );
  transform: rotate(15deg);
  will-change: transform, opacity;
  animation: streak-pass 3s linear infinite;
}

@keyframes streak-pass {
  0% {
    transform: translateY(-10vh) rotate(15deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(110vh) rotate(15deg);
    opacity: 0;
  }
}

/* Typewriter text */
.typewriter-text {
  font-family: 'Orbitron', 'Bebas Neue', monospace;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: opacity;
}

.typewriter-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background: #9333ea;
  margin-left: 4px;
  animation: cursor-blink 0.8s infinite;
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Text flicker effect */
@keyframes text-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.text-flicker {
  animation: text-flicker 1.5s ease-in-out infinite;
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .metallic-logo {
    font-size: 6rem;
  }
  
  .arc-reactor {
    width: 72px;
    height: 72px;
  }
  
  .typewriter-text {
    font-size: 1rem;
  }
  
  /* Reduce particle count on mobile */
  .floating-particle:nth-child(n+11) {
    display: none;
  }
  
  /* Simplify animations on mobile */
  .neon-pulse {
    animation-duration: 3s;
  }
  
  .light-sweep {
    animation-duration: 4s;
  }
}

@media (max-width: 480px) {
  .metallic-logo {
    font-size: 4rem;
  }
  
  .arc-reactor {
    width: 60px;
    height: 60px;
  }
  
  .typewriter-text {
    font-size: 0.9rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .neon-pulse,
  .light-sweep,
  .particle-float,
  .streak-pass,
  .cursor-blink,
  .text-flicker,
  .arc-core-pulse {
    animation: none !important;
  }
  
  .metallic-logo {
    text-shadow: 0 0 20px rgba(147, 51, 234, 0.8);
    filter: drop-shadow(0 0 30px rgba(147, 51, 234, 0.7));
  }
  
  .floating-particle,
  .light-streak {
    display: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .metallic-logo {
    color: #ffffff;
    text-shadow: none;
    filter: none;
  }
  
  .typewriter-text {
    color: #ffffff;
    text-shadow: none;
  }
  
  .arc-reactor {
    border: 2px solid #ffffff;
    box-shadow: none;
  }
}

/* Print styles */
@media print {
  .cinematic-loading {
    display: none !important;
  }
}
