import { Suspense, useState, lazy } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import { Helmet } from 'react-helmet-async';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import CinematicLoadingPage from './components/CinematicLoadingPage';
import LogoTransition, { useLogoTransition } from './components/LogoTransition';
import WebShotEasterEgg from './components/WebShotEasterEgg';
import './styles/cinematic-loading.css';
import Home from './pages/Home';
import About from './pages/About';
import Projects from './pages/Projects';
import Blog from './pages/Blog';
import Contact from './pages/Contact';
import Admin from './pages/Admin';
import MarvelDemo from './pages/MarvelDemo';
import CinematicLoadingDemo from './components/CinematicLoadingDemo';
const Privacy = lazy(() => import('./pages/Privacy'));

export default function App() {
  const location = useLocation();
  const {
    isLoading,
    showTransition,
    transitionComplete,
    handleLoadingComplete,
    handleTransitionComplete
  } = useLogoTransition();

  return (
    <div className="min-h-dvh flex flex-col">
      <Helmet>
        <html lang="en" />
        <meta name="robots" content="index,follow" />
      </Helmet>

      {isLoading ? (
        <CinematicLoadingPage
          onFinish={handleLoadingComplete}
          duration={5000}
        />
      ) : (
        <>
          <a href="#main" className="sr-only focus:not-sr-only focus:fixed focus:top-2 focus:left-2 bg-black text-white px-3 py-2 rounded">Skip to content</a>
          <Navbar />
          <main id="main" className="flex-1 container-px max-w-7xl mx-auto w-full">
            <AnimatePresence mode="wait" initial={false}>
              <motion.div key={location.pathname} initial={{ opacity: 0, y: 8 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -8 }} transition={{ duration: 0.25 }}>
                <Suspense fallback={<div className="p-6">Loading…</div>}>
                  <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/projects" element={<Projects />} />
                    <Route path="/blog" element={<Blog />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="/privacy" element={<Privacy />} />
                    <Route path="/admin" element={<Admin />} />
                    <Route path="/marvel-demo" element={<MarvelDemo />} />
                    <Route path="/cinematic-demo" element={<CinematicLoadingDemo />} />
                  </Routes>
                </Suspense>
              </motion.div>
            </AnimatePresence>
          </main>
          <Footer />

          {/* Web shot easter egg - double click anywhere */}
          <WebShotEasterEgg />
        </>
      )}

      {/* Logo transition from loading to navbar */}
      {showTransition && (
        <LogoTransition
          isLoading={isLoading}
          onTransitionComplete={handleTransitionComplete}
        />
      )}
    </div>
  );
}

