// SpiderManSilhouette.tsx
// Cinematic Spider-Man swing animation for loading screen
// Features: Smooth swing motion, web trail effect, optional toggle

import { motion, useReducedMotion } from "framer-motion";
import { useEffect, useState } from "react";

interface SpiderManSilhouetteProps {
  enabled?: boolean;
  delay?: number;
  className?: string;
}

export default function SpiderManSilhouette({ 
  enabled = true, 
  delay = 1000,
  className = "" 
}: SpiderManSilhouetteProps) {
  const prefersReducedMotion = useReducedMotion();
  const [shouldAnimate, setShouldAnimate] = useState(false);

  useEffect(() => {
    if (!enabled || prefersReducedMotion) return;
    
    const timer = setTimeout(() => {
      setShouldAnimate(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [enabled, delay, prefersReducedMotion]);

  if (!enabled || prefersReducedMotion || !shouldAnimate) {
    return null;
  }

  return (
    <div className={`absolute inset-0 pointer-events-none overflow-hidden ${className}`}>
      {/* Spider-Man silhouette */}
      <motion.div
        className="absolute w-16 h-16 -top-8"
        initial={{ x: -100, y: 100 }}
        animate={{ 
          x: ["-100px", "50vw", "calc(100vw + 100px)"],
          y: ["100px", "30vh", "20vh", "40vh", "100px"]
        }}
        transition={{
          duration: 3,
          ease: [0.25, 0.46, 0.45, 0.94],
          times: [0, 0.3, 1]
        }}
      >
        {/* Spider-Man SVG silhouette */}
        <svg
          viewBox="0 0 64 64"
          className="w-full h-full fill-blue-500/60 drop-shadow-lg"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Simplified Spider-Man silhouette */}
          <path d="M32 8c-4 0-8 2-8 6v8c0 2-1 4-3 5l-5 3c-2 1-3 3-3 5v12c0 4 4 8 8 8h4v8c0 2 2 4 4 4h4c2 0 4-2 4-4v-8h4c4 0 8-4 8-8V35c0-2-1-4-3-5l-5-3c-2-1-3-3-3-5v-8c0-4-4-6-8-6z" />
          {/* Web pattern overlay */}
          <path 
            d="M32 14v6m-6-3h12m-10 8l8-8m-8 0l8 8m-12 6h16m-14 6l12-12m-12 0l12 12"
            stroke="rgba(59, 130, 246, 0.4)"
            strokeWidth="0.5"
            fill="none"
          />
        </svg>
      </motion.div>

      {/* Web trail effect */}
      <motion.div
        className="absolute inset-0"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.6, 0] }}
        transition={{
          duration: 3,
          delay: 0.2,
          ease: "easeInOut"
        }}
      >
        <svg
          className="w-full h-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <defs>
            <linearGradient id="webTrail" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="rgba(59, 130, 246, 0)" />
              <stop offset="30%" stopColor="rgba(59, 130, 246, 0.3)" />
              <stop offset="70%" stopColor="rgba(59, 130, 246, 0.1)" />
              <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
            </linearGradient>
          </defs>
          
          {/* Curved web trail */}
          <motion.path
            d="M-10,50 Q25,20 50,30 Q75,40 110,25"
            stroke="url(#webTrail)"
            strokeWidth="2"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 2.5,
              delay: 0.3,
              ease: "easeOut"
            }}
          />
          
          {/* Secondary trail */}
          <motion.path
            d="M-10,55 Q30,25 55,35 Q80,45 110,30"
            stroke="url(#webTrail)"
            strokeWidth="1"
            fill="none"
            opacity="0.6"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 2.5,
              delay: 0.5,
              ease: "easeOut"
            }}
          />
        </svg>
      </motion.div>

      {/* Web attachment points */}
      <motion.div
        className="absolute top-0 left-1/4 w-1 h-1 bg-blue-400 rounded-full"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: [0, 1.5, 1], opacity: [0, 1, 0.7] }}
        transition={{
          duration: 0.8,
          delay: 0.2,
          ease: "easeOut"
        }}
      />
      
      <motion.div
        className="absolute top-8 right-1/3 w-1 h-1 bg-blue-400 rounded-full"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: [0, 1.5, 1], opacity: [0, 1, 0.7] }}
        transition={{
          duration: 0.8,
          delay: 1.2,
          ease: "easeOut"
        }}
      />

      {/* Subtle web lines connecting to attachment points */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <motion.line
          x1="25%"
          y1="0%"
          x2="50%"
          y2="30%"
          stroke="rgba(59, 130, 246, 0.3)"
          strokeWidth="1"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 0.6 }}
          transition={{
            duration: 1,
            delay: 0.3,
            ease: "easeOut"
          }}
        />
        
        <motion.line
          x1="66%"
          y1="8%"
          x2="55%"
          y2="35%"
          stroke="rgba(59, 130, 246, 0.3)"
          strokeWidth="1"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 0.6 }}
          transition={{
            duration: 1,
            delay: 1.3,
            ease: "easeOut"
          }}
        />
      </svg>
    </div>
  );
}
